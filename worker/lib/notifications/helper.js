/* eslint-disable indent */
const fs = require('fs');
// eslint-disable-next-line import/no-unresolved
const { parse } = require('csv-parse/sync');
const promptsLib = require('../../../lib/prompts');
const User = require('../../../models/user');
const Message = require('../../../models/message');
const AiNotification = require('../../../models/ai-notifications');
const { mapLanguageCodeToName } = require('../../../lib/languages');
const openaiClient = require('../../../lib/openai-client');
const { OpenAI } = require('../../../lib/prompt');

let NOTES = {};
let THEMES = {};
const LABEL_BASE = 'daily-push-ai-v1-';

const client = openaiClient.getOpenaiClient();
const openAi = new OpenAI(client, 'gpt-4o-mini');

const loadThemes = () => {
  try {
    const fileData = parse(fs.readFileSync(`${__dirname}/daywise-theme.csv`, 'utf8'), {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
    });

    fileData.forEach((item) => {
      THEMES[item.Date] = {
        primaryTopic: item['Primary Prompt Topic']?.trim(),
        holidayDetails: item['Holiday Details']?.trim(),
        alternativeTopic: item['Alternative Prompt Topic']?.trim(),
        tone: item['MBTI tone notes']?.trim(),
      };
    });
  } catch (error) {
    console.log(`Error reading daywise-theme.csv: ${error.message}`);
  }
};

const loadNotes = () => {
  try {
    const fileData = parse(fs.readFileSync(`${__dirname}/mbti-notes.csv`, 'utf8'), {
      delimiter: ',',
      columns: true,
      skip_empty_lines: true,
    });

    fileData.forEach((item) => {
      if (!NOTES[item.MBTI]) {
        NOTES[item.MBTI] = {};
      }

      const notesData = {
        notes: item['MBTI plus tone notes']?.trim(),
        warnings: item.Warnings || '',
      };

      for (let i = 1; i <= 13; i++) {
        notesData[i] = item[i.toString()];
      }

      NOTES[item.MBTI] = { ...NOTES[item.MBTI], ...notesData };
    });
  } catch (error) {
    console.log(`Error reading mbti-notes.csv: ${error.message}`);
  }
};

const getMbtiNote = (mbti) => {
  try {
    if (Object.keys(NOTES).length === 0) {
      loadNotes();
    }
    return NOTES[mbti] || null;
  } catch (error) {
    console.log(`Error fetching MBTI note for ${mbti}: ${error.message}`);
    return null;
  }
};

const getTodaysTheme = (key) => {
  try {
    if (Object.keys(THEMES).length === 0) {
      loadThemes();
    }
    return THEMES[key] || null;
  } catch (error) {
    console.log(`Error fetching theme for key ${key}: ${error.message}`);
    return null;
  }
};

const getAnalyticsLabel = (theme) => {
  if (!theme || !theme.primaryTopic) return null;

  const labelOverrides = {
    'User comment': 'comment',
    'User post': 'post',
  };

  const formatLabel = (str) => labelOverrides[str] || str?.toLowerCase().replace(/[\s/]+/g, '-');

  return {
    primary: LABEL_BASE + formatLabel(theme.primaryTopic),
    alternative: LABEL_BASE + formatLabel(theme.alternativeTopic),
  };
};

const getUserBioOrPrompt = (user) => {
  const bioParts = [];
  if (user.description) bioParts.push(user.description);
  if (user.audioDescriptionTranscription) bioParts.push(`AI Transcript: ${user.audioDescriptionTranscription}`);
  const bio = bioParts.length ? `Bio: ${bioParts.join('\n')}` : null;

  let singlePrompt = null;
  if (Array.isArray(user.prompts) && user.prompts.length) {
    const validPrompts = user.prompts
      .map(({ id, answer }) => {
        const promptText = promptsLib.getPromptFromId(id);
        return promptText ? `${promptText}: ${answer}` : null;
      })
      .filter(Boolean);

    if (validPrompts.length) {
      singlePrompt = `Bio Prompt: ${validPrompts[Math.floor(Math.random() * validPrompts.length)]}`;
    }
  }

  const options = [bio, singlePrompt].filter(Boolean);
  return options.length ? options[Math.floor(Math.random() * options.length)] : null;
};

const getUserWisePromptTopic = async (user, key, theme) => {
  const getRandomElements = (arr, n) => {
    if (!Array.isArray(arr) || arr.length === 0) return [];
    return arr.length <= n ? arr : [...arr].sort(() => 0.5 - Math.random()).slice(0, n);
  };

  const formatMedia = ({ isVideo, image, images }) => {
    const hasImage = !isVideo && (image?.length || images?.length);
    return [
      hasImage ? '<image>' : null,
      isVideo ? '<video>' : null,
    ].filter(Boolean);
  };

  switch (key) {
    case 'Holiday':
      return {
        topic: `Holiday: ${theme.holidayDetails}`,
        label: 'holiday',
      };

    case '1 interest':
    case '2 interests':
    case '3 interests': {
      const n = parseInt(key[0], 10);
      const interests = getRandomElements(user.interestNames, n);
      return {
        topic: interests.length ? `Interests: ${interests.join(', ')}` : '',
        label: `${n}-interest${n > 1 ? 's' : ''}`,
      };
    }

    case 'Match activity': {
      const matches = user.metrics?.numMatches ?? 0;
      const weekly = user.weeklyMatchCount ?? 0;

      let topic = matches === 0
        ? 'Match activity: No matches yet, keep going!'
        : weekly > 0
          ? `Match activity: Got ${weekly} matches this week!`
          : 'Match activity: No matches yet this week but had matches before: dry spell';

      return { topic, label: 'match-activity' };
    }

    case 'Chat': {
      if (!user.recentChats?.length) return { topic: null, label: 'chat' };

      const myTurnChats = user.recentChats.filter(
        (chat) => chat.lastMessage?.sender?._id?.toString() !== user._id.toString(),
      );

      const pickRandom = (arr) => arr[Math.floor(Math.random() * arr.length)];
      const chat = pickRandom(myTurnChats.length > 0 ? myTurnChats : user.recentChats);
      const otherUserId = chat.users.find((id) => id !== user._id.toString());

      const [otherUser, messages] = await Promise.all([
        User.findById(otherUserId).select('firstName').lean(),
        Message.find({ chat: chat._id }).sort({ createdAt: -1 }).limit(3).lean(),
      ]);

      const conversation = messages
        .reverse()
        .map((msg) =>
          `${msg.sender?.toString() === user._id?.toString() ? 'User' : otherUser?.firstName}: ${msg.text}`,
        )
        .join('\n');

      const topic = myTurnChats.length > 0
        ? `This match is waiting for your reply: ${chat.lastMessage?.sender?.firstName}\nRecent conversation:\n${conversation}`
        : `User recently chatted with this match: ${otherUser?.firstName}.\nRecent conversation:\n${conversation}`;

      return { topic, label: 'chat' };
    }

    case 'Location':
      return {
        topic: `Location: ${user.city}, ${user.state}, ${user.country}`,
        label: 'location',
      };

    case 'User comment': {
      const comment = user.commentData;
      if (!comment) return { topic: null, label: 'comment' };

      const { parent } = comment;
      const repliedToPost = comment.question?.toString() === parent._id?.toString();
      const parentLabel = repliedToPost ? 'Post' : 'Comment';
      const parentType = repliedToPost ? 'thread' : 'comment';
      const interest = comment.interestName || parent.interestName;
      const interestPart = interest ? ` in ${interest} universe` : '';

      const lines = [];
      if (repliedToPost) {
        if (parent.title?.trim()) lines.push(parent.title.trim());
        if (parent.text?.trim()) lines.push(parent.text.trim());
        lines.push(...formatMedia(parent));
      } else if (parent.text?.trim()) {
        lines.push(parent.text.trim());
      }

      return {
        topic: `User’s recent universe (forum) comment: "${comment.text}" (${comment.numComments || 0} replies, ${comment.numLikes || 0} likes) responding to the following ${parentType}${interestPart}:\n${parentLabel}: ${parent.createdBy?.firstName}: "${lines.join(' ')}"`,
        label: 'comment',
      };
    }

    case 'User post': {
      const post = user.postData;
      if (!post) return { topic: null, label: 'post' };

      const { title, text, interestName, numComments, numLikes } = post;
      const lines = [];
      if (title?.trim()) lines.push(title.trim());
      if (text?.trim()) lines.push(text.trim());
      lines.push(...formatMedia(post));

      return {
        topic: `User’s recent universe (forum) post: ${lines.length ? `"${lines.join(' ')}"` : ''} in ${interestName?.trim()} universe (${numComments} comments, ${numLikes} likes)`,
        label: 'post',
      };
    }

    case 'Bio/Prompt':
      return {
        topic: getUserBioOrPrompt(user, { allowBio: true, allowPrompt: true, pickOne: true }),
        label: 'bio-prompt',
      };

    default:
      return { topic: null, label: 'unknown' };
  }
};

const prepareProfile = (user, theme) => {
  const profileParts = [];
  if (user.age) profileParts.push(`Age: ${user.age}`);
  if (user.gender) profileParts.push(`Gender: ${user.gender}`);

  const preferences = [];
  const formatPreference = (type, values, subPreferences) => {
    if (!values?.length) return null;

    const isOnlyNonBinary = values.length === 1 && values[0] === 'non-binary';
    const mainText = subPreferences?.length ? `${type}: ${subPreferences}` : type;

    return isOnlyNonBinary ? mainText : `${mainText} with ${values.join(', ')}`;
  };

  const datingPreference = formatPreference('Dating', user.preferences?.dating, user.datingSubPreferences);
  const friendsPreference = formatPreference('Friends', user.preferences?.friends);

  if (datingPreference) preferences.push(datingPreference);
  if (friendsPreference) preferences.push(friendsPreference);
  if (preferences.length) profileParts.push(`Looking For: ${preferences.join(' / ')}`);

  const mbtiNote = getMbtiNote(user.personality?.mbti);
  if (mbtiNote) {
    const tovNote = mbtiNote[theme.tone];
    profileParts.push(
      `MBTI Personality Type: ${user.personality.mbti} ${mbtiNote.notes} ${tovNote}.${mbtiNote.warnings.length ? ` ${mbtiNote.warnings}` : ''}`,
    );
  }

  return profileParts.join('\n');
};

const preparePrompt = async (user, theme) => {
  let isAlternative = false;
  let { topic, label } = await getUserWisePromptTopic(user, theme.primaryTopic, theme);
  if (!topic) {
    const altResult = await getUserWisePromptTopic(user, theme.alternativeTopic, theme);
    if (altResult?.topic) {
      isAlternative = true;
      label = altResult.label;
    }
  }

  const promptText = `I need an engaging push notification for Boo, a dating/friendship app. The notification should be uniquely tailored to the specific user based on their MBTI personality type, the Prompt Topic indicated below, and whether they're looking for dating or friendship and with which gender e.g. find a beautiful girl, hot guy, anime chick. Do not imply the user has new messages or matches that they didn’t know about. The output must include:

- A short engaging or intriguing title (4-5 words)
- A concise message that encourages them to open the app to match or message someone or to engage in the universe (forums) (max 20 words)

The output should be formatted as a json object in the following format in ${mapLanguageCodeToName(user.locale || 'en')}: { output: ["title", "message"] }

Please provide a push notification that feels natural, engaging and optimized to this user, and the target person they're looking for. Prioritize authenticity and do not overuse exclamation points.

Here are the user's details:

${prepareProfile(user, theme)}${topic?.length ? `\n\nPrompt Topic:\n\n${topic}` : ''}`;

  return { promptText, primaryTopic: theme.primaryTopic, alternativeTopic: theme.alternativeTopic, isAlternative, label };
};

const generateAiNotification = async (user, theme) => {
  const result = {};
  try {
    let isError = false;
    let errorMessage = null;
    let cost = 0;

    const { promptText, primaryTopic, alternativeTopic, isAlternative, label } = await preparePrompt(user, theme);

    const analyticsLabel = LABEL_BASE + label;
    const response = await openAi.executePrompt({
      prompt: promptText,
      response_format: { type: 'json_object' },
    });

    if (response) {
      isError = response.errorMessage !== undefined;
      errorMessage = response.errorMessage;
      cost = response.cost || 0;
      if (response.output) {
        try {
          const parsed = JSON.parse(response.output.replace('```json', '').replace('```', ''));
          result.output = parsed?.output;
        } catch (err) {
          isError = true;
          errorMessage = `json parsing error: ${err.message}`;
        }
      }
    }
    result.analyticsLabel = analyticsLabel;
    await new AiNotification({
      user: user._id,
      primaryTopic,
      alternativeTopic,
      isAlternative,
      analyticsLabel,
      prompt: promptText,
      output: response.output,
      isError,
      errorMessage,
      cost,
      response: JSON.stringify(response),
    }).save();
  } catch (error) {
    console.log(`Error generating AI notification: ${error.message} for user ${user._id}`);
  }
  return result;
};

module.exports = { getTodaysTheme, getAnalyticsLabel, generateAiNotification };
