const { expect } = require('chai');
const request = require('supertest');
const { app } = require('./common');
const User = require('../models/user');
const { renameEthnicity } = require('../lib/rename-ethnicity');

const oldName = 'Chinese';
const newName = 'Han Chinese';

describe('rename ethnicities', async () => {
  beforeEach(async () => {
    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
      .send({ appVersion: '1.13.24' })
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql();
  });

  it('old name not found', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Korean']);

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Korean']);
  });

  it('old name in ethnicities field', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', oldName, 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', oldName, 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', newName, 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(null);
  });

  it('old name in preferences.ethnicities field', async () => {
    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', oldName, 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', oldName, 'Korean']);

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql();
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', newName, 'Korean']);
  });

  it('substring should not match', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Chinese Tatar', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Chinese Tatar', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();

    await renameEthnicity(oldName, newName);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Chinese Tatar', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql();
  });

  it('rename Berber to Amazigh', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
  });

  it('remove duplicates after renaming', async () => {
    res = await request(app)
    .put('/v1/user/ethnicities')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean', 'Amazigh'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
    .patch('/v1/user/preferences')
      .set('authorization', 0)
      .send({
        ethnicities: ['Asian', 'Berber', 'Korean', 'Amazigh'],
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .put('/v1/user/initApp')
      .set('authorization', 0)
    expect(res.status).to.equal(200);
    expect(res.body.user.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
    expect(res.body.user.preferences.ethnicities).to.eql(['Asian', 'Amazigh', 'Korean']);
  });
});

