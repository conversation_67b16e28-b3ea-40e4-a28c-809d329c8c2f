const {
  app,
  validGif,
  validResponseGif,
  validTenorGif,
  validTenorResponseGif,
  validImagePath,
  initSocket,
  destroySocket,
  getSocketPromise,
} = require('./common');
const mongoose = require('mongoose');
const { notifs, reset, waitFor, fakeAdminMessaging, setMockImageModerationResponse } = require('./stub');
const { expect } = require('chai');
const { assert } = require('chai');
const sinon = require('sinon');
const request = require('supertest');
const temp = require('temp').track();
const fs = require('fs');
const hive = require('../lib/hive');
const User = require('../models/user');
const UserMetadata = require('../models/user-metadata');
const Action = require('../models/action');
const Chat = require('../models/chat');
const Message = require('../models/message');
const MessageLog = require('../models/message-log');
const StickerPack = require('../models/sticker-pack');
const Report = require('../models/report');
const MessageReport = require('../models/message-report');
const constants = require('../lib/constants');
const coinsConstants = require('../lib/coins-constants');
const chatLib = require('../lib/chat');
const { validGoogleReceipt, validAppleReceipt } = require('./iap');
const { purchaseStickerPack, initApp, setPersonality, sendUserLike, approveUser, getIndividualChat } = require('./helper/api');
const { getValidGoogleReceipt } = require('./helper/iap');
const { getMockStickerId, getMockStickerPackId } = require('./helper/sticker-packs');
const message = require('../models/message');
const userLib = require('../lib/user');

describe('text fix on empty message edit', () => {
  beforeEach(async () => {
    let res = await request(app).put('/v1/user/initApp').set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 1);
    expect(res.status).to.equal(200);
  });

  it('test sending empty or only blank space message', async () => {
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post(`/v1/message`).set('authorization', 0).send({
      user: '1',
      text: `send message to user 1`,
    });
    expect(res.status).to.equal(200);

    res = await request(app).post(`/v1/message`).set('authorization', 0).send({
      user: '1',
      text: ``,
    });
    expect(res.status).to.equal(400);

    res = await request(app).post(`/v1/message`).set('authorization', 0).send({
      user: '1',
      text: `   `,
    });
    expect(res.status).to.equal(400);
  });

  it('test editing empty message', async () => {
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post(`/v1/message`).set('authorization', 0).send({
      user: '1',
      text: `send message to user 1`,
    });
    expect(res.status).to.equal(200);

    let message = await Message.findById(res.body._id);
    expect(message.edited).to.equal(undefined);
    expect(message.editLogs).to.equal(undefined);
    message.text = '';
    await message.save();

    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({
      messageId: message._id,
      text: `editing empty message`,
    });
    expect(res.status).to.equal(200);

    message = await Message.findById(message._id);
    expect(message).to.have.property('editLogs');
    expect(message.edited).to.equal(true);
    expect(message.editLogs[0].text).to.equal('');
  });
});

describe('test fix reply error', () => {
  beforeEach(async () => {
    let res = await request(app).put('/v1/user/initApp').set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 1);
    expect(res.status).to.equal(200);
  });

  it('test reply with quoted message with quoted story', async () => {
    let res = await request(app).put('/v1/user/fcmToken').set('authorization', 0).send({
      fcmToken: 'token0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/fcmToken').set('authorization', 1).send({
      fcmToken: 'token1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/firstName').set('authorization', 1).send({ firstName: `name1` });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/story').set('authorization', 0).attach('image', validImagePath).field({ visibility: 'everyone', backgroundColor: 1 });
    expect(res.status).to.equal(200);
    const storyId = res.body.story._id;

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/sendDirectMessage').set('authorization', 1).send({
      user: '0',
      message: 'Message with quoted story',
      price: 50,
      quotedStory: storyId,
    });
    expect(res.status).to.equal(200);

    const messages = await Message.find({});
    expect(messages.length).to.equal(1);
    const messageId = messages[0]._id;

    const socket1 = await initSocket(1);
    const socketPromise1 = getSocketPromise(socket1, 'message');
    res = await request(app)
      .post(`/v1/message?quotedMessageId=${messageId}`)
      .set('authorization', 0)
      .send({
        user: '1',
        text: `reply with quoted message with quoted story ${messageId}`,
      });
    expect(res.status).to.equal(200);
    expect(res.body.quotedMessage).to.not.have.property('quotedStory');

    res = await socketPromise1;
    expect(res.quotedMessage).to.not.have.property('quotedStory');
    await destroySocket(socket1);
  });
});

describe('test unverified users support chat', async () => {
  beforeEach(async () => {
    constants.enforceVerification.restore();
    sinon.stub(constants, 'enforceVerification').returns(true);
    let res = await request(app).get('/v1/user').set('authorization', chatLib.BOO_SUPPORT_ID);
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 0);
    expect(res.status).to.equal(200);
  });

  afterEach(async () => {
    constants.enforceVerification.restore();
    sinon.stub(constants, 'enforceVerification').returns(false);
  });

  it('test unverified users to send messages to support chat', async () => {
    let res = await request(app).post('/v1/feedback').set('authorization', 0).send({
      message: 'feedback',
    });
    expect(res.status).to.equal(200);

    const chats = await Chat.find({ users: '0' });
    const chatId = chats[0]._id;

    const user = await User.findById('0');
    expect(user.isVerified()).to.equal(false);
    expect(constants.enforceVerification()).to.equal(true);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: chatLib.BOO_SUPPORT_ID,
      text: `messaged by 0 msg test`,
    });
    expect(res.status).to.equal(200);
    expect(res.body.chat).to.equal(chatId.toString());
  });

  it('test unverified users can not send messages to other chats', async () => {
    let res = await request(app).put('/v1/user/initApp').set('authorization', 1);
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    const user = await User.findById('0');
    expect(user.isVerified()).to.equal(false);
    expect(constants.enforceVerification()).to.equal(true);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: `messaged by user 0 to user 1`,
    });
    expect(res.status).to.equal(403);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: `messaged by user 1 to user 0`,
    });
    expect(res.status).to.equal(403);
  });
});

describe('message edit tests', () => {
  let chatId, messagesByUser0, messagesByUser1;
  beforeEach(async () => {
    messagesByUser0 = [];
    messagesByUser1 = [];
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/fcmToken').set('authorization', uid).send({
        fcmToken: 'token',
      });
      expect(res.status).to.equal(200);
    }
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);
    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);
    for (let i = 0; i < 5; i++) {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: `messaged by 0 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser0.push(res.body._id);
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: `messaged by 1 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser1.push(res.body._id);
      chatId = res.body.chat;
    }
  });

  it('should return error if text or messageId invalid', async () => {
    // No text
    let res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ messageId: messagesByUser0[0] });
    expect(res.status).to.equal(422);

    // No messageId
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Something new' });
    expect(res.status).to.equal(422);

    // Invalid messageId
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Something new', messageId: 'invalid' });
    expect(res.status).to.equal(422);

    // text length more than 10000 characters
    res = await request(app)
      .patch(`/v1/message/edit`)
      .set('authorization', 0)
      .send({ text: 'a'.repeat(10001), messageId: messagesByUser0[0] });
    expect(res.status).to.equal(422);

    // message is unsent by the user, so should not be able to edit
    await Message.updateOne({ _id: messagesByUser0[0] }, { unsent: true });
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Something new', messageId: messagesByUser0[0] });
    expect(res.status).to.equal(403);

    // Sending an image message to user 1
    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(200);
    messagesByUser0.push(res.body._id);

    // Image message should not be edited
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Something new', messageId: res.body._id });
    expect(res.status).to.equal(403);

    // User1 should not be able to edit a message sent by user 0
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 1).send({ text: 'Something new', messageId: messagesByUser0[0] });
    expect(res.status).to.equal(403);
  });

  it('should return error if message/chat not found', async () => {
    const nonExistentId = new mongoose.Types.ObjectId().toString();
    let res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Edited Message', messageId: nonExistentId });
    expect(res.status).to.equal(404);

    await Chat.deleteOne({ _id: chatId });

    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: 'Edited Message', messageId: messagesByUser0[0] });
    expect(res.status).to.equal(404);
  });

  it('should check edit message in the chat', async () => {
    const socket = await initSocket(1);
    const socketPromise = getSocketPromise(socket, 'message edited');
    let message = await Message.findById(messagesByUser0[0]);
    const originalText = message.text;

    const updatedText1 = 'This message was edited by user 0';
    // Edit message sent by user 0
    await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: updatedText1, messageId: messagesByUser0[0] });

    // Verify socket event for message edited
    const socketResponse = await socketPromise;
    expect(socketResponse).to.eql({
      messageId: messagesByUser0[0],
      chatId,
      updatedMessage: updatedText1,
    });
    await destroySocket(socket);

    // Check if the message is updated for user 1
    let res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });

    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[9].text).to.equal(updatedText1);
    expect(res.body[9].edited).to.equal(true);
    expect(res.body[9].editLogs).to.equal(undefined);

    // Check if the message is updated for user 0
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });

    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[9].text).to.equal(updatedText1);
    expect(res.body[9].edited).to.equal(true);
    expect(res.body[9].editLogs).to.equal(undefined);

    // Verify previous text is stored correctly
    message = await Message.findById(messagesByUser0[0]);
    console.log(message.editLogs);
    expect(message.editLogs).to.have.lengthOf(1);
    expect(message.editLogs[0].text).to.equal(originalText);
    expect(message.editLogs[0]).to.have.property('editedAt');
    expect(message.edited).to.equal(true);
    expect(message.text).to.equal(updatedText1);

    const updatedText2 = 'This message was edited by user 0 again';
    await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: updatedText2, messageId: messagesByUser0[0] });

    message = await Message.findById(messagesByUser0[0]);
    expect(message.editLogs).to.have.lengthOf(2);
    expect(message.editLogs[0].text).to.equal(originalText);
    expect(message.editLogs[0]).to.have.property('editedAt');
    expect(message.editLogs[1].text).to.equal(updatedText1);
    expect(message.editLogs[1]).to.have.property('editedAt');
    expect(message.edited).to.equal(true);
    expect(message.text).to.equal(updatedText2);
  });

  it('should log and report the message if keywords are found', async () => {
    let newText = 'This message contains boo keyword';

    let res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: newText, messageId: messagesByUser0[0] });
    expect(res.status).to.equal(200);

    // await for few milliseconds to wait for logs
    await new Promise((resolve) => setTimeout(resolve, 200));

    const logs = await MessageLog.find({});
    expect(logs.length).to.equal(1);
    expect(logs[0].sender).to.equal('0');
    expect(logs[0].text).to.equal(newText);
    expect(logs[0].keywords.includes('boo')).to.equal(true);
    expect(logs[0].chat.toString()).to.equal(chatId);

    newText = 'This message contains whatsapp keywords';
    const user0 = await User.findById(0);
    user0.verification.method = 'pose';
    await user0.save();

    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: newText, messageId: messagesByUser0[0] });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 200));
    const report = await Report.findOne({ reportedUser: '0' });
    expect(report.status).to.equal('needs review');
    expect(report.reason[0]).to.equal('Auto-report due to message keyword');

    user0.verification.method = undefined;
    await user0.save();

    newText = 'This message contains send me money keywords';
    res = await request(app).patch(`/v1/message/edit`).set('authorization', 0).send({ text: newText, messageId: messagesByUser0[0] });
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 200));

    const reports = await MessageReport.find({});
    expect(reports.length).to.equal(1);
    expect(reports[0].user).to.equal('0');
    expect(reports[0].keyword).to.equal('send me money');
  });
});

describe('testing message unsent in one to one chat', () => {
  let chatId, messagesByUser0, messagesByUser1;
  beforeEach(async () => {
    messagesByUser0 = [];
    messagesByUser1 = [];
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/fcmToken').set('authorization', uid).send({
        fcmToken: 'token',
      });
      expect(res.status).to.equal(200);
    }
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);
    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);
    for (let i = 0; i < 5; i++) {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: `messaged by 0 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser0.push(res.body._id);
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: `messaged by 1 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser1.push(res.body._id);
      chatId = res.body.chat;
    }
  });
  it('should check unsent message in the chat', async () => {
    // unsending a message sent by user 0
    let res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 0);
    expect(res.status).to.equal(200);

    // checking if the message is unsent for user 1
    res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[9].text).to.equal('This message was unsent');

    // checking if the message is unsent for user 0
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[9].text).to.equal('This message was unsent');
  });
  it('should check unsent last message in the chat', async () => {
    // unsending last message sent by user 1
    let res = await request(app)
      .patch(`/v1/message/unsend?messageId=${messagesByUser1[messagesByUser1.length - 1]}`)
      .set('authorization', 1);
    expect(res.status).to.equal(200);

    // checking if the message is unsent for user 0
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[0].text).to.equal('This message was unsent');

    // checking if the message is unsent for user 1
    res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(10);
    expect(res.body[0].text).to.equal('This message was unsent');

    // check last message in chat list for both users
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage.text).to.equal('This message was unsent');

    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage.text).to.equal('This message was unsent');
  });
  it('should check unsent image message in the chat', async () => {
    // sending image message
    let res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(200);
    messagesByUser0.push(res.body._id);

    // unsending last image message sent by user 0
    res = await request(app)
      .patch(`/v1/message/unsend?messageId=${messagesByUser0[messagesByUser0.length - 1]}`)
      .set('authorization', 0);
    expect(res.status).to.equal(200);

    // checking if the message is unsent for user 0
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('This message was unsent');

    // checking if the message is unsent for user 1
    res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal('This message was unsent');

    // check last message in chat list for both users
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage.text).to.equal('This message was unsent');

    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage.text).to.equal('This message was unsent');
  });
  // Test unsent message can not be unsent again
  it('should check unsent message can not be unsent again', async () => {
    // unsending a message sent by user 0
    let res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 0);
    expect(res.status).to.equal(200);

    // unsending the same message again
    res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 0);
    expect(res.status).to.equal(400);

    // Try to unsent the message from user 1
    res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 1);
    expect(res.status).to.equal(400);
  });
});

describe('testing message unsent in group chat', () => {
  const numUsers = 4;
  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let res = await request(app).put('/v1/user/initApp').set('authorization', uid).send({ appVersion: '1.13.49' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/fcmToken').set('authorization', uid).send({
        fcmToken: 'token',
      });
      expect(res.status).to.equal(200);
    }
  });
  it('should create a group chat, send a message and delete by sender', async () => {
    for (let uid = 1; uid < numUsers; uid++) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: `${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app).patch('/v1/user/approve').set('authorization', uid).send({
        user: '0',
      });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .patch('/v1/chat/createGroupChat')
      .set('authorization', 0)
      .send({ users: ['0', '1', '2', '3'] });
    expect(res.status).to.equal(200);
    expect(res.body).haveOwnProperty('chat');
    const chatId = res.body.chat._id;

    res = await request(app).post(`/v1/message?chatId=${chatId}`).set('authorization', 0).send({
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect(res.body).haveOwnProperty('_id');
    expect(res.body).haveOwnProperty('chat');
    const messageId = res.body._id;

    res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body[0]._id).to.equal(messageId);

    // deleted by sender
    res = await request(app).patch(`/v1/message/unsend?messageId=${messageId}`).set('authorization', 0);
    expect(res.status).to.equal(200);

    // deleted for all users in the chat
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app).get('/v1/message').set('authorization', uid).query({ chatId });
      expect(res.status).to.equal(200);
      expect(res.body.length).to.equal(2);
      expect(res.body[0].text).to.equal('This message was unsent');
    }

    res = await request(app).post(`/v1/message?chatId=${chatId}`).set('authorization', 1).send({
      text: 'msg2',
    });
    expect(res.status).to.equal(200);
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app).get('/v1/message').set('authorization', uid).query({ chatId });
      expect(res.status).to.equal(200);
      expect(res.body.length).to.equal(3);
      expect(res.body[0].text).to.equal('msg2');
    }
  });
});

describe('testing message unsent for latest app versions', () => {
  let chatId, messagesByUser0, messagesByUser1;
  beforeEach(async () => {
    messagesByUser0 = [];
    messagesByUser1 = [];
    for (let uid = 0; uid < 2; uid++) {
      let res = await request(app).put('/v1/user/initApp').set('authorization', uid).send({ appVersion: '1.13.49' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name ${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/fcmToken').set('authorization', uid).send({
        fcmToken: 'token',
      });
      expect(res.status).to.equal(200);
    }
    let res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);
    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);
    for (let i = 0; i < 5; i++) {
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 0)
        .send({
          user: '1',
          text: `messaged by 0 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser0.push(res.body._id);
      res = await request(app)
        .post('/v1/message')
        .set('authorization', 1)
        .send({
          user: '0',
          text: `messaged by 1 msg ${i}`,
        });
      expect(res.status).to.equal(200);
      messagesByUser1.push(res.body._id);
      chatId = res.body.chat;
    }
  });
  it('should check unsent property for message in the chat', async () => {
    let res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 0);
    expect(res.status).to.equal(200);

    // checking if the message is unsent for user 1
    res = await request(app).get('/v1/message').set('authorization', 1).query({ chatId });
    expect(res.status).to.equal(200);
    res.body.forEach((message) => {
      if (message._id === messagesByUser0[0]) {
        expect(message.unsent).to.equal(true);
      }
    });
  });
  it('should check notification and socket event structure for unsent message', async () => {
    const socket = await initSocket(1);
    const socketPromise = getSocketPromise(socket, 'unsent message');

    // Message unsent notification is disabled for now
    // const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake((params) => new Promise((resolve, reject) => {
    //   if (params.token === 'invalidToken') {
    //     return reject(new Error('Fake error'));
    //   }
    //   resolve({ response: 'success' });
    // }));

    let res = await request(app).patch(`/v1/message/unsend?messageId=${messagesByUser0[0]}`).set('authorization', 0);
    expect(res.status).to.equal(200);
    await new Promise((r) => setTimeout(r, 1000));

    // const message = await Message.findOne({ _id: messagesByUser0[0] });

    // // updated notification structure
    // const params = {
    //   token: 'token',
    //   data: {
    //     action: 'delete',
    //     notificationId: `${message.notificationId}`,
    //   },
    //   fcmOptions: { analyticsLabel: 'chat-message-delete' },
    // };
    // sinon.assert.calledOnce(sendStub);
    // sinon.assert.calledWith(sendStub, params);
    res = await socketPromise.catch((err) => {
      console.log(err);
    });
    expect(res).to.eql({
      messageId: messagesByUser0[0],
      chatId,
    });
    await destroySocket(socket);
  });
  it('should match notification payload structure with old notification structure', async () => {
    let res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect(res.body).to.haveOwnProperty('_id');
    expect(res.body).to.haveOwnProperty('chat');
    const messageId = res.body._id;

    const sendStub = sinon.stub(fakeAdminMessaging, 'send').callsFake(
      (params) =>
        new Promise((resolve, reject) => {
          console.log('Fake messaging().send() ', params);
          if (params.token === 'invalidToken') {
            return reject(new Error('Fake error'));
          }
          resolve({ response: 'success' });
        }),
    );

    await new Promise((r) => setTimeout(r, 1000));

    const message = await Message.findOne({ _id: messageId }).lean();
    const user = await User.findOne({ _id: '0' });
    const formatted = {
      chat: message.chat,
      sender: message.sender,
      text: message.text,
      _id: message._id,
      createdAt: message.createdAt,
      reactions: message.reactions,
      __v: message.__v,
    };

    // Previous notification structure
    const params = {
      token: 'token',
      notification: {
        title: user.firstName,
        body: message.text,
      },
      apns: {
        payload: {
          aps: {
            sound: 'love.wav',
            badge: 7,
            'thread-id': message.chat.toString(),
          },
        },
      },
      android: {
        priority: 'high',
        notification: {
          channelId: 'love',
          sound: 'love.wav',
        },
      },
      data: {
        message: JSON.stringify(formatted),
      },
      fcmOptions: { analyticsLabel: 'chat-message' },
    };

    /* New structure
    const params = {
      token: 'token',
      data: {
        body: message.text,
        notificationId: `${message.notificationId}`,
        message: JSON.stringify(formatted),
        title: user.firstName,
      },
      fcmOptions: { analyticsLabel: 'chat-message' },
    };
    */
    sinon.assert.calledOnce(sendStub);
    sinon.assert.calledWith(sendStub, params);
  });
});

describe('messaging', () => {
  const numUsers = 2;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/personality').set('authorization', uid).send({
        mbti: 'ESTJ',
      });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/fcmToken').set('authorization', uid).send({
        fcmToken: 'token',
      });
      expect(res.status).to.equal(200);
    }

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);
  });

  it('messages and gifs without timestamp', async () => {
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    // invalid gif
    res = await request(app).post('/v1/message/gif').set('authorization', 0).send({
      recipient: '1',
      gif: 'yahoo.com',
    });
    expect(res.status).to.equal(422);

    // giphy url in query parameter should be rejected
    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: `https://invalid.com?q=${validGif}`,
      });
    expect(res.status).to.equal(422);

    // valid gif
    res = await request(app).post('/v1/message/gif').set('authorization', 0).send({
      recipient: '1',
      gif: validGif,
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);

    // valid gif
    res = await request(app).post('/v1/message/gif').set('authorization', 0).send({
      recipient: '1',
      gif: validTenorGif,
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body[0].gif).to.equal(validTenorResponseGif);
    expect(res.body[1].text).to.equal('msg2');
    expect(res.body[2].gif).to.equal(validResponseGif);
    expect(res.body[3].text).to.equal('msg1');
  });

  it('tenor gifs', async () => {
    const tenorGif = 'https://media.tenor.com/Z17Yj4dVzfMAAAAC/snoopy-dog.gif';

    // tenor url in query parameter should be rejected
    res = await request(app)
      .post('/v1/message/gif')
      .set('authorization', 0)
      .send({
        recipient: '1',
        gif: `https://invalid.com?q=${tenorGif}`,
      });
    expect(res.status).to.equal(422);

    res = await request(app).post('/v1/message/gif').set('authorization', 0).send({
      recipient: '1',
      gif: tenorGif,
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body[0].gif).to.equal('https://media.tenor.com/Z17Yj4dVzfMAAAAM/snoopy-dog.gif');
  });

  it('karma award for first to message', async () => {
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 0 })).karma).to.equal(5);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 0 })).karma).to.equal(5);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect((await User.findOne({ _id: 0 })).karma).to.equal(5);
    expect((await User.findOne({ _id: 1 })).karma).to.equal(0);
  });

  it('karma award for first to message - daily limit', async () => {
    // set user premium
    user = await User.findOne({ _id: 0 });
    user.premiumExpiration = Date.now() + 86400000;
    await user.save();

    for (let i = 2; i <= 6; i++) {
      res = await request(app).put('/v1/user/initApp').set('authorization', i);
      expect(res.status).to.equal(200);

      res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
        user: i.toString(),
      });
      expect(res.status).to.equal(200);

      res = await request(app).patch('/v1/user/approve').set('authorization', i).send({
        user: '0',
      });
      expect(res.status).to.equal(200);

      res = await request(app).post('/v1/message').set('authorization', 0).send({
        user: i.toString(),
        text: 'msg1',
      });
      expect(res.status).to.equal(200);
    }

    expect((await User.findOne({ _id: 0 })).karma).to.equal(20);

    // reset current day
    user = await User.findOne({ _id: 0 });
    user.metrics.currentDayResetTime = Date.now();
    await user.save();

    res = await request(app).put('/v1/user/initApp').set('authorization', 0);
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 7);
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '7',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 7).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '7',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    expect((await User.findOne({ _id: 0 })).karma).to.equal(25);
  });

  it('messages and stickers', async () => {
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    // invalid sticker
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: 1234,
    });
    expect(res.status).to.equal(422);

    // sticker not found
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: 'not_found',
    });
    expect(res.status).to.equal(404);

    const premiumStickerId = getMockStickerId(1, 3);

    // fails with 403 because premium sticker pack not purchased
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: premiumStickerId,
    });
    expect(res.status).to.equal(403);

    // purchasing sticker pack 0
    await purchaseStickerPack(0, getValidGoogleReceipt(getMockStickerPackId(1), Date.now()));

    // success
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: premiumStickerId,
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body[0].text).to.equal('msg2');
    expect(res.body[1].sticker).to.equal(premiumStickerId);
    expect(res.body[2].text).to.equal('msg1');

    // don't need to buy free sticker packs
    const freeStickerId = getMockStickerId(0, 2);
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: freeStickerId,
    });
    expect(res.status).to.equal(200);
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);

    console.log(res.body);
    expect(res.body[0].sticker).to.equal(freeStickerId);
    expect(res.body[1].text).to.equal('msg2');
    expect(res.body[2].sticker).to.equal(premiumStickerId);
    expect(res.body[3].text).to.equal('msg1');
  });

  it('purchase sticker pack with coins', async () => {
    const premiumStickerId = getMockStickerId(1, 3);

    // not enough coins to purchase
    await UserMetadata.updateOne({ user: '0' }, { coins: 0 });

    res = await request(app)
      .put('/v1/coins/stickerPack')
      .set('authorization', 0)
      .send({ price: coinsConstants.stickerPackCost, productId: getMockStickerPackId(1) });
    expect(res.status).to.equal(403);

    // enough coins, but invalid productId
    await UserMetadata.updateOne({ user: '0' }, { coins: coinsConstants.stickerPackCost });

    res = await request(app).put('/v1/coins/stickerPack').set('authorization', 0).send({ price: coinsConstants.stickerPackCost, productId: 'invalid' });
    expect(res.status).to.equal(404);

    // success
    res = await request(app)
      .put('/v1/coins/stickerPack')
      .set('authorization', 0)
      .send({ price: coinsConstants.stickerPackCost, productId: getMockStickerPackId(1) });
    expect(res.status).to.equal(200);

    // verify coin count updated
    res = await request(app).put('/v1/user/initApp').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.user.coins).to.equal(0);

    // verify able to send premium sticker
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: premiumStickerId,
    });
    expect(res.status).to.equal(200);
  });

  it('json stickers should be converted to gif for versions < 1.11.62', async () => {
    // create sticker pack with json stickers
    await StickerPack.insertMany([
      {
        packName: 'json',
        productId: 'json',
        stickers: [{ id: 'json/json.json' }],
      },
    ]);

    // send json sticker
    res = await request(app).post('/v1/message/sticker').set('authorization', 0).send({
      user: '1',
      sticker: 'json/json.json',
    });
    expect(res.status).to.equal(200);

    // old version should see gif sticker
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].sticker).to.equal('json/json.gif');

    // upgrade version
    res = await request(app).put('/v1/user/initApp').set('authorization', 1).send({ appVersion: '1.11.62' });
    expect(res.status).to.equal(200);

    // new version should see json sticker
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].sticker).to.equal('json/json.json');
  });

  it('mark messages seen', async () => {
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(1);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(2);

    res = await request(app).put('/v1/message/seen').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);
  });

  it('chat id instead of user id', async () => {
    // create third user
    res = await request(app).get('/v1/user').set('authorization', 2);
    expect(res.status).to.equal(200);

    // get chat id
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    chatId = res.body[0]._id;

    // third user not in chat cannot access it
    res = await request(app).post('/v1/message').set('authorization', 2).query({ chatId }).send({
      text: 'msg2',
    });
    expect(res.status).to.equal(404);

    res = await request(app).get('/v1/message').set('authorization', 2).query({ chatId });
    expect(res.status).to.equal(404);

    res = await request(app).put('/v1/message/seen').set('authorization', 2).query({ chatId });
    expect(res.status).to.equal(200);

    // users in chat can access it
    res = await request(app).post('/v1/message').set('authorization', 1).query({ chatId }).send({
      text: 'msg2',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);

    res = await request(app).put('/v1/message/seen').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);

    // cannot access chat id that does not exist
    res = await request(app).post('/v1/message').set('authorization', 0).query({ chatId: 'fake' }).send({
      text: 'msg2',
    });
    expect(res.status).to.equal(422);

    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId: 'fake' });
    expect(res.status).to.equal(422);

    res = await request(app).put('/v1/message/seen').set('authorization', 0).query({ chatId: 'fake' });
    expect(res.status).to.equal(422);
  });

  it('mark chat seen when getting messages', async () => {
    // get chat id
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(1);
    chatId = res.body[0]._id;

    // get messages
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);

    // unread messages is cleared
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(0);
  });

  it('long message', async () => {
    const longMessage = 'x'.repeat(5000);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: longMessage,
        createdAt: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body[0].text).to.equal(longMessage);
  });

  it('message log', async () => {
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'great app',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'yeah',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'This app never notifies me',
    });
    expect(res.status).to.equal(200);

    const logs = await MessageLog.find().sort('createdAt');
    expect(logs.length).to.equal(2);
    expect(logs[0].text).to.equal('great app');
    expect(logs[0].keywords).to.eql(['app']);
    expect(logs[0].sender).to.equal('0');
    expect(logs[1].text).to.equal('This app never notifies me');
    expect(logs[1].keywords).to.eql(['app', 'notif']);
    expect(logs[1].sender).to.equal('1');
  });

  /*
  it('hidden message', async () => {
    await new Promise((r) => setTimeout(r, 100));
    reset();

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'check out ur my type',
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].hidden).to.equal();

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('auto-hide keywords', async () => {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['en'] });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));
    reset();

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'transfer money to me',
      });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    res = await request(app)
      .get('/v1/chat')
      .set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.equal(undefined);

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 0)
      .query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].hidden).to.equal();

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('skip auto-hide keywords if user language is not English', async () => {
    res = await request(app)
      .put('/v1/user/languages')
      .set('authorization', 0)
      .send({ languages: ['de', 'en'] });
    expect(res.status).to.equal(200);

    res = await request(app)
      .post('/v1/message')
      .set('authorization', 0)
      .send({
        user: '1',
        text: 'transfer money to me',
      });
    expect(res.status).to.equal(200);

    res = await request(app)
      .get('/v1/message')
      .set('authorization', 1)
      .query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
  });
  */

  it('image', async () => {
    // no image provided
    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0);
    expect(res.status).to.equal(422);

    // invalid file type
    buffer = 'a'.repeat(100);
    imagePath = temp.openSync({ suffix: '.mp3' }).path;
    fs.writeFileSync(imagePath, buffer);

    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', imagePath);
    expect(res.status).to.equal(400);

    // TODO: test file too large
    // the below doesn't work - multer gets stuck
    /*
    buffer = 'a'.repeat(10 * 1000 * 1000);
    imagePath = temp.openSync({suffix: '.png'}).path;
    fs.writeFileSync(imagePath, buffer);

    res = await request(app)
      .post('/v1/message/image')
      .query({recipient: 1})
      .set('authorization', 0)
      .attach('image', imagePath)
    expect(res.status).to.equal(400);
    */

    // valid image
    buffer = 'a'.repeat(100);
    imagePath = temp.openSync({ suffix: '.png' }).path;
    fs.writeFileSync(imagePath, buffer);

    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', imagePath);
    expect(res.status).to.equal(200);
    assert(res.body.image.includes(`${constants.IMAGE_DOMAIN}chats/${res.body.chat}/`));
    const savedMessage = res.body;
    expect(res.body).to.eql({
      text: savedMessage.text,
      _id: savedMessage._id,
      createdAt: savedMessage.createdAt,
      chat: savedMessage.chat,
      image: savedMessage.image,
      reactions: [],
      sender: '0',
      __v: 0,
    });

    // check chat lastMessage
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.eql(savedMessage);

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0]).to.eql(savedMessage);
  });

  it('image with aspect ratio', async () => {
    res = await request(app)
      .post('/v1/message/image')
      .query({ recipient: 1 })
      .set('authorization', 0)
      .attach('image', validImagePath)
      .field({ aspectRatio: 0.75 });
    expect(res.status).to.equal(200);
    expect(res.body.aspectRatio).to.equal(0.75);

    // check chat lastMessage
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage.aspectRatio).to.equal(0.75);

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].aspectRatio).to.equal(0.75);
  });

  it('inappropriate image', async () => {
    const response = {
      isFlagged: true,
      detectionLabels: [
        {
          ParentName: 'Explicit Nudity',
          Name: 'Exposed Male Genitalia',
          Confidence: 95,
        },
      ],
      flaggedModerationLabel: {
        ParentName: 'Explicit Nudity',
        Name: 'Exposed Male Genitalia',
        Confidence: 95,
      },
    };
    setMockImageModerationResponse(response);

    // picture upload should fail
    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(422);
    expect(res.text).to.equal('There’s an issue with this photo, please select another one.');

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('audio', async () => {
    // no file provided
    res = await request(app).post('/v1/message/audio').query({ recipient: 1 }).set('authorization', 0);
    expect(res.status).to.equal(422);

    // invalid file type
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.dng' }).path;
    fs.writeFileSync(filePath, buffer);

    res = await request(app).post('/v1/message/audio').query({ recipient: 1 }).set('authorization', 0).attach('audio', filePath);
    expect(res.status).to.equal(400);

    // invalid file type
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.jpg' }).path;
    fs.writeFileSync(filePath, buffer);

    res = await request(app).post('/v1/message/audio').query({ recipient: 1 }).set('authorization', 0).attach('audio', filePath);
    expect(res.status).to.equal(400);

    // TODO: test file too large

    // valid file
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.mp3' }).path;
    fs.writeFileSync(filePath, buffer);

    res = await request(app).post('/v1/message/audio').query({ recipient: 1 }).set('authorization', 0).attach('audio', filePath);
    expect(res.status).to.equal(200);
    console.log(res.body);
    assert(res.body.audio.includes(`${constants.IMAGE_DOMAIN}chats/${res.body.chat}/`));
    let savedMessage = res.body;
    expect(res.body).to.eql({
      text: savedMessage.text,
      _id: savedMessage._id,
      createdAt: savedMessage.createdAt,
      chat: savedMessage.chat,
      audio: savedMessage.audio,
      reactions: [],
      sender: '0',
      __v: 0,
    });

    // check chat lastMessage
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.eql(savedMessage);

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0]).to.eql(savedMessage);

    // with waveform
    res = await request(app)
      .post('/v1/message/audio')
      .query({ recipient: 1 })
      .set('authorization', 0)
      .attach('audio', filePath)
      .field({ waveform: JSON.stringify([1.3, 1.5]), duration: 1.5 });
    expect(res.status).to.equal(200);
    console.log(res.body);
    assert(res.body.audio.includes(`${constants.IMAGE_DOMAIN}chats/${res.body.chat}/`));
    savedMessage = res.body;
    expect(res.body).to.eql({
      text: savedMessage.text,
      _id: savedMessage._id,
      createdAt: savedMessage.createdAt,
      chat: savedMessage.chat,
      audio: savedMessage.audio,
      audioWaveform: [1.3, 1.5],
      audioDuration: 1.5,
      reactions: [],
      sender: '0',
      __v: 0,
    });

    // check chat lastMessage
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.eql(savedMessage);

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    expect(res.body[0]).to.eql(savedMessage);
  });

  it('video', async () => {
    // no file provided
    res = await request(app).post('/v1/message/video').query({ recipient: 1 }).set('authorization', 0);
    expect(res.status).to.equal(422);

    // invalid file type
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.jpg' }).path;
    fs.writeFileSync(filePath, buffer);

    res = await request(app).post('/v1/message/video').query({ recipient: 1 }).set('authorization', 0).attach('video', filePath);
    expect(res.status).to.equal(400);

    // TODO: test file too large

    // valid file
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.mp4' }).path;
    fs.writeFileSync(filePath, buffer);

    res = await request(app).post('/v1/message/video').query({ recipient: 1 }).set('authorization', 0).attach('video', filePath).field({ aspectRatio: 0.75 });
    expect(res.status).to.equal(200);
    console.log(res.body);
    assert(res.body.video.includes(`${constants.IMAGE_DOMAIN}chats/${res.body.chat}/`));
    const savedMessage = res.body;
    expect(res.body).to.eql({
      text: savedMessage.text,
      _id: savedMessage._id,
      createdAt: savedMessage.createdAt,
      chat: savedMessage.chat,
      video: savedMessage.video,
      aspectRatio: 0.75,
      reactions: [],
      sender: '0',
      __v: 0,
    });

    // check chat lastMessage
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].lastMessage).to.eql(savedMessage);

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0]).to.eql(savedMessage);
  });

  it('inappropriate video upload', async () => {
    const stub = hive.moderateVideo.callsFake((params) => {
      console.log('Fake moderateVideo', JSON.stringify(params));
      const impl = function (resolve, reject) {
        resolve({
          body: {
            status: [
              {
                response: {
                  output: [
                    {
                      classes: [
                        {
                          class: 'general_nsfw',
                          score: 0.91,
                        },
                      ],
                    },
                  ],
                },
              },
            ],
          },
        });
      };
      return new Promise(impl);
    });

    res = await request(app).post('/v1/message/video').query({ recipient: 1 }).set('authorization', 0).attach('image', validVideoPath);
    expect(res.status).to.equal(422);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(0);
  });

  it('reply to message', async () => {
    const userTypes = ['myself', 'other'];
    const messageTypes = ['text', 'gif', 'image'];

    for (const userType of userTypes) {
      for (const quotedMessageType of messageTypes) {
        for (const replyMessageType of messageTypes) {
          console.log(userType, quotedMessageType, replyMessageType);

          // send original message
          sender = '0';
          recipient = '1';
          if (quotedMessageType == 'text') {
            res = await request(app).post('/v1/message').set('authorization', sender).send({
              user: recipient,
              text: 'text',
            });
            expect(res.status).to.equal(200);
          } else if (quotedMessageType == 'gif') {
            res = await request(app).post('/v1/message/gif').set('authorization', sender).send({
              recipient,
              gif: validGif,
            });
            expect(res.status).to.equal(200);
          } else if (quotedMessageType == 'image') {
            res = await request(app).post('/v1/message/image').set('authorization', sender).query({ recipient }).attach('image', validImagePath);
            expect(res.status).to.equal(200);
          }
          console.log(res.body);
          quotedMessage = res.body;

          // send reply
          if (userType == 'other') {
            sender = '1';
            recipient = '0';
          }
          if (replyMessageType == 'text') {
            res = await request(app).post('/v1/message').set('authorization', sender).query({ quotedMessageId: quotedMessage._id }).send({
              user: recipient,
              text: 'text',
            });
            expect(res.status).to.equal(200);
          } else if (replyMessageType == 'gif') {
            res = await request(app).post('/v1/message/gif').set('authorization', sender).query({ quotedMessageId: quotedMessage._id }).send({
              recipient,
              gif: validGif,
            });
            expect(res.status).to.equal(200);
          } else if (replyMessageType == 'image') {
            res = await request(app)
              .post('/v1/message/image')
              .set('authorization', sender)
              .query({
                recipient,
                quotedMessageId: quotedMessage._id,
              })
              .attach('image', validImagePath);
            expect(res.status).to.equal(200);
          }
          console.log(res.body);
          replyMessage = res.body;

          expect(replyMessage.quotedMessage).to.eql(quotedMessage);

          // check chats
          res = await request(app).get('/v1/chat/approved').set('authorization', 1);
          expect(res.status).to.equal(200);
          expect(res.body.chats.length).to.equal(1);
          expect(res.body.chats[0].lastMessage.quotedMessage).to.equal();

          // check messages
          res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
          expect(res.status).to.equal(200);
          console.log(res.body);
          expect(res.body.length).to.equal(2);
          expect(res.body[0]).to.eql(replyMessage);
          expect(res.body[1]).to.eql(quotedMessage);

          // delete messages
          await Message.deleteMany();
        }
      }
    }
  });

  it('reply to reply', async () => {
    // send original message
    sender = '0';
    recipient = '1';
    res = await request(app).post('/v1/message').set('authorization', sender).send({
      user: recipient,
      text: 'text',
    });
    expect(res.status).to.equal(200);
    console.log(res.body);
    quotedMessage = res.body;

    // send reply
    res = await request(app).post('/v1/message').set('authorization', sender).query({ quotedMessageId: quotedMessage._id }).send({
      user: recipient,
      text: 'text',
    });
    expect(res.status).to.equal(200);
    replyMessage = res.body;

    expect(replyMessage.quotedMessage).to.eql(quotedMessage);

    // send reply to reply
    res = await request(app).post('/v1/message').set('authorization', sender).query({ quotedMessageId: replyMessage._id }).send({
      user: recipient,
      text: 'text',
    });
    expect(res.status).to.equal(200);
    replyToReplyMessage = res.body;

    expect(replyToReplyMessage.quotedMessage._id).to.equal(replyMessage._id);
    expect(replyToReplyMessage.quotedMessage.quotedMessage).to.equal();

    // check chats
    res = await request(app).get('/v1/chat/approved').set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.chats.length).to.equal(1);
    expect(res.body.chats[0].lastMessage.quotedMessage).to.equal();

    // check messages
    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log(res.body);
    expect(res.body.length).to.equal(3);
    expect(res.body[0]).to.eql(replyToReplyMessage);
    expect(res.body[1]).to.eql(replyMessage);
    expect(res.body[2]).to.eql(quotedMessage);
  });

  it('invalid quotedMessageId', async () => {
    // should go through without the quote
    res = await request(app).post('/v1/message').set('authorization', 0).query({ quotedMessageId: 'test' }).send({
      user: '1',
      text: 'text',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').query({ user: 0 }).set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    expect(res.body[0].quotedMessage).to.eql();
  });

  it('chat streak', async () => {
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numMessages).to.equal(0);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numMessages).to.equal(1);

    res = await request(app).post('/v1/message/gif').set('authorization', 1).send({
      recipient: '0',
      gif: validGif,
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numMessages).to.equal(2);
  });

  it('backfill num messages', async () => {
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    const chat = await Chat.findOne();
    chat.numMessages = undefined;
    await chat.save();

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numMessages).to.equal(0);

    await chatLib.backfillNumMessages();

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numMessages).to.equal(1);
  });

  it('read receipts', async () => {
    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'chat read receipt');

    // no read receipt if not premium
    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal();

    const chatId = res.body.chats[0]._id;

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal();

    // purchase premium
    res = await request(app).put('/v1/user/purchasePremium').set('authorization', 0).send({ receipt: validGoogleReceipt });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal(0);

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal(0);

    // send new message to user 1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal(1);

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal(1);

    // user 1 sees the message
    res = await request(app).put('/v1/message/seen').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal(0);

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal(0);

    res = await socketPromise.catch((err) => {
      console.error(err);
    });
    expect(res).to.eql({
      _id: chatId,
      partnerNumUnreadMessages: 0,
    });

    await destroySocket(socket);
  });

  it('no read receipt socket event if not premium', async () => {
    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'chat read receipt', 100);

    // send new message to user 1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    // user 1 sees the message
    res = await request(app).put('/v1/message/seen').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    // no socket read receipt
    res = await socketPromise.catch((err) => {
      console.error(err);
    });
    expect(res).to.eql();

    await destroySocket(socket);
  });

  it('hide read receipts', async () => {
    // make both users premium
    res = await request(app).put('/v1/user/purchasePremium').set('authorization', 0).send({ receipt: validGoogleReceipt });
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/purchasePremium').set('authorization', 1).send({ receipt: validAppleReceipt });
    expect(res.status).to.equal(200);

    // user 1 hides read receipts
    res = await request(app).put('/v1/user/hideReadReceipts').set('authorization', 1).send({ hideReadReceipts: true });
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.hideReadReceipts).to.equal(true);

    socket = await initSocket(0);
    socketPromise = getSocketPromise(socket, 'chat read receipt', 100);

    // send new message to user 1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);

    // user 0 does not see read receipt
    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal();

    const chatId = res.body.chats[0]._id;

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal();

    // user 1 sees the message
    res = await request(app).put('/v1/message/seen').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    // no socket read receipt
    res = await socketPromise.catch((err) => {
      console.error(err);
    });
    expect(res).to.eql();

    await destroySocket(socket);

    // remove premium from user 1
    user = await User.findOne({ _id: 1 });
    user.premiumExpiration = undefined;
    await user.save();

    // hide read receipts should be disabled
    res = await request(app).put('/v1/user/initApp').set('authorization', 1);
    expect(res.status).to.equal(200);
    expect(res.body.user.hideReadReceipts).to.equal(false);

    // user 0 sees read receipt
    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.chats[0].partnerNumUnreadMessages).to.equal(0);

    res = await request(app).get('/v1/chat/readReceipt').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    expect(res.body.partnerNumUnreadMessages).to.equal(0);
  });

  it('call log', async () => {
    res = await request(app).get('/v1/chat/approved').set('authorization', 0);
    expect(res.status).to.equal(200);
    const chatId = res.body.chats[0]._id;

    // test all combinations of input
    for (const type of ['voice', 'video']) {
      for (const status of ['canceled', 'unanswered', 'answered']) {
        for (const duration of [0, 10]) {
          res = await request(app).post('/v1/message/call').set('authorization', 0).query({ chatId }).send({
            type,
            status,
            duration,
          });
          expect(res.status).to.equal(200);

          res = await request(app).get('/v1/message').query({ chatId }).set('authorization', 1);
          expect(res.status).to.equal(200);
          expect(res.body[0].sender).to.equal('0');
          expect(res.body[0].call).to.eql({
            type,
            status,
            duration,
          });
        }
      }
    }

    // invalid input
    res = await request(app).post('/v1/message/call').set('authorization', 0).query({ chatId }).send({
      type: 'invalid',
      status: 'invalid',
      duration: 'invalid',
    });
    expect(res.status).to.equal(422);
  });

  it('initiate call', async () => {
    await new Promise((r) => setTimeout(r, 100));
    reset();

    res = await request(app).post('/v1/message/initiateCall').set('authorization', 0).send({
      user: '1',
      session_id: 'id',
    });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token');
    expect(notifs.recent.notification.title).to.equal('');
    expect(notifs.recent.notification.body).to.equal('name0 is calling you.');
    expect(notifs.recent.data).to.eql({
      session_id: 'id',
      user_name: 'name0',
      media: 'video',
      signal_type: 'startCall',
      caller_name: 'name0',
      caller_id: '12345',
      call_type: '1',
      call_opponents: '{43208,20802}',
      ios_voip: '1',
    });
    expect(notifs.numSent).to.equal(1);
  });

  it('block', async () => {
    // get chat id
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    const chatId = res.body[0]._id;

    res = await request(app).patch('/v1/user/block').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).get('/v1/message').set('authorization', 0).query({ user: 1 });
    expect(res.status).to.equal(404);

    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(404);
  });

  it('keyword report - money - dismiss', async () => {
    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [
              {
                message: {
                  content: '{"ban": false, "explanation": "not scam"}',
                },
              },
            ],
          };
        },
      },
    };

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'hello',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'send to my paypal',
    });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    reports = await MessageReport.find();
    expect(reports.length).to.equal(1);
    console.log(reports[0]);
    expect(reports[0].user).to.equal('0');
    expect(reports[0].messages).to.equal('[\n  "Reported User: send to my paypal",\n  "Reporter: hello"\n]');
    expect(reports[0].keyword).to.equal('paypal');
    expect(reports[0].decision).to.equal('dismiss');
    expect(reports[0].openai.explanation).to.equal('not scam');
  });

  it('keyword report - money - ban', async () => {
    fakeOpenaiClient.chat = {
      completions: {
        async create(params) {
          console.log(`Fake openai chat completions: ${JSON.stringify(params)}`);
          return {
            usage: {
              prompt_tokens: 10,
              completion_tokens: 10,
            },
            choices: [
              {
                message: {
                  content: '{"ban": true, "explanation": "scam"}',
                },
              },
            ],
          };
        },
      },
    };

    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'hello',
    });
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(200);

    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'send to my paypal',
    });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 100));

    reports = await MessageReport.find();
    expect(reports.length).to.equal(1);
    console.log(reports[0]);
    expect(reports[0].user).to.equal('0');
    expect(reports[0].messages).to.equal(
      '[\n  "Reported User: send to my paypal",\n  "Reported User: name0 sent you a picture! [update to latest version to view]",\n  "Reporter: hello"\n]',
    );
    expect(reports[0].keyword).to.equal('paypal');
    expect(reports[0].decision).to.equal('shadow_ban');
    expect(reports[0].openai.explanation).to.equal('scam');

    user = await User.findById('0');
    expect(user.shadowBanned).to.equal(true);
    expect(user.bannedBy).to.equal('openai');
    expect(user.bannedReason).to.equal('asking for money in messages');
    expect(user.bannedNotes).to.equal(`MessageReport _id: ${reports[0]._id}. Explanation: scam`);

    // admin checks reports
    user = await User.findOne({ _id: 0 });
    user.admin = true;
    user.adminPermissions = { all: true };
    await user.save();

    res = await request(app).get('/v1/admin/user/messageReports').set('authorization', 0).query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.reports.length).to.equal(1);
    console.log(res.body.reports[0]);
    expect(res.body.reports[0].openai.ban).to.equal(true);
    expect(res.body.reports[0].openai.explanation).to.equal('scam');
  });

  it('dndMessage', async () => {
    const setProfileNotifications = async function (userId, params) {
      return await request(app).patch('/v1/user/profile/notifications').set('authorization', userId).send(params);
    };

    res = await getIndividualChat(0, { user: 1 });
    expect(res.chat.dndMessage).to.eql(undefined); //dndMessage false by default
    expect(res.chat.dndPost).to.eql(undefined); //dndPost false by default

    await initApp(0, { appVersion: '1.11.75' });
    res = await getIndividualChat(0, { user: 1 });
    expect(res.chat.dndMessage).to.eql(false); //dndMessage false by default
    expect(res.chat.dndPost).to.eql(false); //dndPost false by default

    res = await request(app).get('/v1/user/profileDetails').set('authorization', 0).query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);
    expect(res.body.user.dndMessage).to.equal(false);
    expect(res.body.user.dndPost).to.equal(false);

    await initApp(2, { appVersion: '1.11.75' });
    await setPersonality(2, { mbti: 'ESTJ' });
    await sendUserLike(2, { user: '0' });

    await approveUser(0, { user: '2' });

    res = await getIndividualChat(0, { user: 2 });
    expect(res.chat.dndMessage).to.eql(false); //dndMessage false by default
    expect(res.chat.dndPost).to.eql(false); //dndPost false by default

    res = await setProfileNotifications(0, { user: '3' });
    expect(res.status).to.eql(404); //not found error

    res = await setProfileNotifications(0, { user: '2' });
    expect(res.status).to.eql(422); //invalid input error as no keys to modify

    res = await setProfileNotifications(0, { user: '2', dndMessage: 'aaa' });
    expect(res.status).to.eql(422); //invalid input error

    res = await setProfileNotifications(0, { user: '2', dndMessage: true });
    expect(res.status).to.eql(200); //success
    expect(res.body).to.eql({});

    //no change for user 1
    res = await getIndividualChat(0, { user: '1' });
    expect(res.chat.dndMessage).to.eql(false);
    expect(res.chat.dndPost).to.eql(false);

    res = await request(app).get('/v1/user/profileDetails').set('authorization', 0).query({ user: '1' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);
    expect(res.body.user.dndMessage).to.equal(false);
    expect(res.body.user.dndPost).to.equal(false);

    //user 2 settings updated
    res = await getIndividualChat(0, { user: '2' });
    expect(res.chat.dndMessage).to.eql(true);
    expect(res.chat.dndPost).to.eql(false);

    res = await request(app).get('/v1/user/profileDetails').set('authorization', 0).query({ user: '2' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);
    expect(res.body.user.dndMessage).to.equal(true);
    expect(res.body.user.dndPost).to.equal(false);

    // check on other side
    res = await getIndividualChat(2, { user: '0' });
    expect(res.chat.dndMessage).to.eql(false);
    expect(res.chat.dndPost).to.eql(false);

    res = await request(app).get('/v1/user/profileDetails').set('authorization', 2).query({ user: '0' });
    expect(res.status).to.equal(200);
    expect(res.body.user.isMatched).to.equal(true);
    expect(res.body.user.dndMessage).to.equal(false);
    expect(res.body.user.dndPost).to.equal(false);

    reset();

    res = await request(app).post('/v1/message').set('authorization', 2).send({
      user: '0',
      text: 'how are you?',
    });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0); //no notifications received

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(1); //new message received by user 0

    //disable dndMessage
    res = await setProfileNotifications(0, { user: '2', dndMessage: false });
    expect(res.status).to.eql(200); //success
    expect(res.body).to.eql({});

    //no change for user 1
    res = await getIndividualChat(0, { user: '1' });
    expect(res.chat.dndMessage).to.eql(false);
    expect(res.chat.dndPost).to.eql(false);

    //user 2 settings updated
    res = await getIndividualChat(0, { user: '2' });
    expect(res.chat.dndMessage).to.eql(false);
    expect(res.chat.dndPost).to.eql(false);

    reset();

    res = await request(app).post('/v1/message').set('authorization', 2).send({
      user: '0',
      text: 'how are you ???',
    });
    expect(res.status).to.equal(200);

    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(1); //notifications received
    expect(notifs.recent.token).to.equal('token');
    expect(notifs.recent.notification.body).to.equal('how are you ???');

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body[0].numUnreadMessages).to.equal(2); //new message received by user 0
  });
});

describe('reactions', () => {
  const numUsers = 2;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app).put('/v1/user/initApp').set('authorization', uid).send({ appVersion: '1.13.77' });
      expect(res.status).to.equal(200);

      res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/personality').set('authorization', uid).send({
        mbti: 'ESTJ',
      });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: `token${uid}`,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    //user0 send message to user1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    console.log('after sent msg1: ', res.body);
    chatId = res.body.chat;
    msg1Id = res.body._id;

    //user1 send message to user0
    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);
    console.log('after sent msg2: ', res.body);
    msg2Id = res.body._id;
  });

  it('user with app version below 1.13.77, should not receive notification & numUnreadMessage should not increase', async () => {
    //set user0 version app to 1.13.76
    res = await request(app).put('/v1/user/initApp').set('authorization', 0).send({ appVersion: '1.13.76' });
    expect(res.status).to.equal(200);

    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(2);

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction).to.be.undefined;
    expect(res.body[0].numUnreadMessages).to.equal(2);
  });

  it('reactions on received text message', async () => {
    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);
    chatId = res.body[0]._id;

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(1);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[0].reaction).to.equal('❤️');

    //user1 try to add same reaction, should be failed
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(422);

    //user1 add different reaction
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    notificationData = {};

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);

    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('😘');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
    expect(res.body[0].numUnreadMessages).to.equal(1);

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(2);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[1].sender).to.equal('1');
    expect(msg1.reactions[0].reaction).to.equal('❤️');
    expect(msg1.reactions[1].reaction).to.equal('😘');

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction).to.be.undefined;

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(1);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[0].reaction).to.equal('❤️');
  });

  it('removed first reaction', async () => {
    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);
    chatId = res.body[0]._id;

    //user1 try to add same reaction, should be failed
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(422);

    //user1 add different reaction
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('😘');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
    expect(res.body[0].numUnreadMessages).to.equal(4);

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
  });

  it('remove reaction on message that only has one reaction', async () => {
    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);
    chatId = res.body[0]._id;

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction).to.be.undefined;
    //numUnreadMessages decreased after reaction removed
    expect(res.body[0].numUnreadMessages).to.equal(2);
    chatId = res.body[0]._id;
  });

  it('reaction on message who send by deleted user', async () => {
    //user0 delete account
    user0 = await User.findOne({ _id: 0 });
    await userLib.deleteAccount(user0);

    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    //no notification since user was deleted
    expect(notifs.numSent).to.equal(0);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 1);
    expect(res.status).to.equal(200);
    console.log('user0 chat page: ', res.body);
    expect(res.body.length).to.equal(0);
  });

  it('reactions on sended text message', async () => {
    //user1 react on msg2
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😅',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😅');
    chatId = res.body[0]._id;

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg2 = res.body.find((msg) => msg._id === msg2Id);
    console.log('msg2: ', msg2);
    expect(msg2.reactions.length).to.equal(1);
    expect(msg2.reactions[0].sender).to.equal('1');
    expect(msg2.reactions[0].reaction).to.equal('😅');

    //user1 add different reaction
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
    chatId = res.body[0]._id;

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg2 = res.body.find((msg) => msg._id === msg2Id);
    console.log('msg2: ', msg2);
    expect(msg2.reactions.length).to.equal(2);
    expect(msg2.reactions[0].sender).to.equal('1');
    expect(msg2.reactions[1].sender).to.equal('1');
    expect(msg2.reactions[0].reaction).to.equal('😅');
    expect(msg2.reactions[1].reaction).to.equal('😘');

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(1);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction).to.be.undefined;
    chatId = res.body[0]._id;

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg2 = res.body.find((msg) => msg._id === msg2Id);
    console.log('msg2: ', msg2);
    expect(msg2.reactions.length).to.equal(1);
    expect(msg2.reactions[0].sender).to.equal('1');
    expect(msg2.reactions[0].reaction).to.equal('😅');
  });

  it('invalid reaction', async () => {
    // all text
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: 'not an emoji',
    });
    expect(res.status).to.equal(422);

    // text + emoji
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: 'hi 😅',
    });
    expect(res.status).to.equal(422);

    // emoji + text
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😅 hi',
    });
    expect(res.status).to.equal(422);

    // more than one emoji
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😅 😘',
    });
    expect(res.status).to.equal(422);

    // space
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: ' ',
    });
    expect(res.status).to.equal(422);
  });

  it('invalid messageId', async () => {
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: 'test',
      reaction: '😘',
    });
    expect(res.status).to.equal(422);
  });

  it('user never put reaction but try to remove reaction', async () => {
    //user1 put reaction 😅 on msg2Id
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg2Id,
      reaction: '😅',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);
    msg = await Message.findById(msg2Id);
    console.log('message: ', msg);
    expect(msg.reactions.length).to.equal(1);
    expect(msg.reactions[0].sender).to.equal('1');
    expect(msg.reactions[0].reaction).to.equal('😅');

    //user0 try to remove reaction on msg2Id
    res = await request(app).delete('/v1/message/reaction').set('authorization', 0).send({
      messageId: msg2Id,
    });
    expect(res.status).to.equal(422);

    msg = await Message.findById(msg2Id);
    console.log('message: ', msg);
    expect(msg.reactions.length).to.equal(1);
    expect(msg.reactions[0].sender).to.equal('1');
    expect(msg.reactions[0].reaction).to.equal('😅');
  });

  it('reaction on image, video, and audio', async () => {
    //user0 send image to user1
    res = await request(app).post('/v1/message/image').query({ recipient: 1 }).set('authorization', 0).attach('image', validImagePath);
    expect(res.status).to.equal(200);
    console.log('after sent image msg3: ', res.body);
    msg3Id = res.body._id;

    //user1 react on msg3 image
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg3Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    msg = await Message.findById(msg3Id);
    console.log('message: ', msg);
    expect(msg.reactions.length).to.equal(1);
    expect(msg.reactions[0].sender).to.equal('1');
    expect(msg.reactions[0].reaction).to.equal('❤️');

    // valid file
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.mp3' }).path;
    fs.writeFileSync(filePath, buffer);

    //user0 send audio to user1
    res = await request(app).post('/v1/message/audio').query({ recipient: 1 }).set('authorization', 0).attach('audio', filePath);
    expect(res.status).to.equal(200);
    console.log('after sent audio msg4: ', res.body);
    msg4Id = res.body._id;

    //user1 react on msg4 audio
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg4Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    msg = await Message.findById(msg4Id);
    console.log('message: ', msg);
    expect(msg.reactions.length).to.equal(1);
    expect(msg.reactions[0].sender).to.equal('1');
    expect(msg.reactions[0].reaction).to.equal('❤️');

    // valid file
    buffer = 'a'.repeat(100);
    filePath = temp.openSync({ suffix: '.mp4' }).path;
    fs.writeFileSync(filePath, buffer);

    //user0 send video to user1
    res = await request(app).post('/v1/message/video').query({ recipient: 1 }).set('authorization', 0).attach('video', filePath).field({ aspectRatio: 0.75 });
    expect(res.status).to.equal(200);
    console.log('after sent video msg4: ', res.body);
    msg5Id = res.body._id;

    //user1 react on msg4 audio
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg5Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    msg = await Message.findById(msg5Id);
    console.log('message: ', msg);
    expect(msg.reactions.length).to.equal(1);
    expect(msg.reactions[0].sender).to.equal('1');
    expect(msg.reactions[0].reaction).to.equal('❤️');
  });

  it('should not able to put reaction on boo bot message', async () => {
    let res = await request(app).put('/v1/user/initApp').set('authorization', chatLib.BOO_BOT_ID);
    expect(res.status).to.equal(200);

    res = await request(app).put('/v1/user/initApp').set('authorization', 0).send({ appVersion: '1.13.76' });
    expect(res.status).to.equal(200);

    // Upload a picture
    res = await request(app).post('/v1/user/picture/v2').set('authorization', '0').attach('image', validImagePath);
    expect(res.status).to.equal(200);

    // Picture uploaded but not verified, should trigger welcome message from Boo
    res = await request(app).patch('/v1/user/events').set('authorization', 0).send({ finished_signup: true });
    expect(res.status).to.equal(200);

    await new Promise((r) => setTimeout(r, 20));
    let messages = await Message.find({ sender: chatLib.BOO_BOT_ID }).sort({ createdAt: -1 });
    expect(messages.length).to.equal(2); // Whats new message is also sent
    botMsgId = messages[0]._id;

    //user0 react on msg4 audio
    res = await request(app).put('/v1/message/reaction').set('authorization', 0).send({
      messageId: botMsgId,
      reaction: '❤️',
    });
    expect(res.status).to.equal(403);
  });

  it('reactions notification for bangla', async () => {
    let user = await User.findById(0);
    user.locale = 'bn';
    await user.save();

    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 আপনার বার্তায় প্রতিক্রিয়া জানিয়েছেন');
    expect(notifs.recent.notification.body).to.equal('❤️');
  });

  it('reactions notification for japanese', async () => {
    let user = await User.findById(0);
    user.locale = 'ja';
    await user.save();

    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 があなたのメッセージに反応しました');
    expect(notifs.recent.notification.body).to.equal('❤️');
  });
});

describe('mute chat and messages notification', async () => {
  const numUsers = 3;

  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      res = await request(app).put('/v1/user/initApp').set('authorization', uid).send({ appVersion: '1.13.77' });
      expect(res.status).to.equal(200);

      res = await request(app).get('/v1/user').set('authorization', uid);
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app).put('/v1/user/personality').set('authorization', uid).send({
        mbti: 'ESTJ',
      });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: `token${uid}`,
        });
      expect(res.status).to.equal(200);
    }

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '1',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
      user: '2',
    });
    expect(res.status).to.equal(200);

    res = await request(app).patch('/v1/user/approve').set('authorization', 2).send({
      user: '0',
    });
    expect(res.status).to.equal(200);

    //user0 send message to user1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    console.log('after sent msg1: ', res.body);
    chatId = res.body.chat;
    msg1Id = res.body._id;

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('name0');
    expect(notifs.recent.notification.body).to.equal('msg1');

    //user1 send message to user0
    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg2',
    });
    expect(res.status).to.equal(200);
    console.log('after sent msg2: ', res.body);
    msg2Id = res.body._id;

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1');
    expect(notifs.recent.notification.body).to.equal('msg2');

    //user0 send message to user2
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '2',
      text: 'msg10',
    });
    expect(res.status).to.equal(200);
    console.log('after sent msg1: ', res.body);
    chatId2 = res.body.chat;

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token2');
    expect(notifs.recent.notification.title).to.equal('name0');
    expect(notifs.recent.notification.body).to.equal('msg10');
  });

  it('message - user mute chat', async () => {
    //user0 muted chat withh user1
    //user0 mute messages notification
    res = await request(app).patch('/v1/user/profile/notifications').set('authorization', 0).send({ user: '1', dndMessage: true });
    expect(res.status).to.equal(200);

    //user0 send second message to user 1
    res = await request(app).post('/v1/message').set('authorization', 0).send({
      user: '1',
      text: 'msg3',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token1');
    expect(notifs.recent.notification.title).to.equal('name0');
    expect(notifs.recent.notification.body).to.equal('msg3');

    //user1 send second message to user0
    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg4',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);
  });

  it('user mute global messages notification', async () => {
    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);
    chatId = res.body[0]._id;

    //user0 mute messages notification
    res = await request(app)
      .put('/v1/user/notificationSettings')
      .set('authorization', 0)
      .send({
        pushNotificationSettings: { messages: false },
      });
    expect(res.status).to.equal(200);

    //user1 add different reaction
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
    expect(res.body[0].numUnreadMessages).to.equal(4);

    //user1 remove reaction
    res = await request(app).delete('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
  });

  it('reaction - user mute chat notification', async () => {
    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);
    chatId = res.body[0]._id;

    //user0 mute chat notification from user1
    expect(res.status).to.equal(200);
    res = await request(app).patch('/v1/user/profile/notifications').set('authorization', 0).send({ user: '1', dndMessage: true });
    expect(res.status).to.equal(200);

    //user1 add different reaction
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😘',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.numSent).to.equal(0);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg2');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😘');
    expect(res.body[0].numUnreadMessages).to.equal(4);
  });

  it('user0 mute chat with with user2, should still receive notification on chat with user1', async () => {
    //user2 send message to user0
    res = await request(app).post('/v1/message').set('authorization', 2).send({
      user: '0',
      text: 'msg11',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name2');
    expect(notifs.recent.notification.body).to.equal('msg11');

    //user0 mute chat with user2
    res = await request(app).patch('/v1/chat/mute').set('authorization', 0).send({
      chatId: chatId2,
    });
    expect(res.status).to.equal(200);

    //user2 send message to user0
    res = await request(app).post('/v1/message').set('authorization', 2).send({
      user: '0',
      text: 'msg12',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);

    //user1 send message to user0
    res = await request(app).post('/v1/message').set('authorization', 1).send({
      user: '0',
      text: 'msg13',
    });
    expect(res.status).to.equal(200);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1');
    expect(notifs.recent.notification.body).to.equal('msg13');

    //user1 react on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal('name1 reacted to your message');
    expect(notifs.recent.notification.body).to.equal('❤️');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(2);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg13');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.firstName).to.equal('name1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(4);
  });
});

describe('reactions in group chat', () => {
  const numUsers = 4;
  beforeEach(async () => {
    for (let uid = 0; uid < numUsers; uid++) {
      let res = await request(app).put('/v1/user/initApp').set('authorization', uid).send({ appVersion: '1.13.77' });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/firstName')
        .set('authorization', uid)
        .send({
          firstName: `name${uid}`,
        });
      expect(res.status).to.equal(200);
      res = await request(app)
        .put('/v1/user/fcmToken')
        .set('authorization', uid)
        .send({
          fcmToken: `token${uid}`,
        });
      expect(res.status).to.equal(200);
    }
  });
  it('all members put reactions', async () => {
    for (let uid = 1; uid < numUsers; uid++) {
      let res = await request(app)
        .patch('/v1/user/sendLike')
        .set('authorization', 0)
        .send({
          user: `${uid}`,
        });
      expect(res.status).to.equal(200);

      res = await request(app).patch('/v1/user/approve').set('authorization', uid).send({
        user: '0',
      });
      expect(res.status).to.equal(200);
    }

    let res = await request(app)
      .patch('/v1/chat/createGroupChat')
      .set('authorization', 0)
      .send({ users: ['0', '1', '2', '3'] });
    expect(res.status).to.equal(200);
    expect(res.body).haveOwnProperty('chat');
    const chatId = res.body.chat._id;

    res = await request(app).post(`/v1/message?chatId=${chatId}`).set('authorization', 0).send({
      text: 'msg1',
    });
    expect(res.status).to.equal(200);
    expect(res.body).haveOwnProperty('_id');
    expect(res.body).haveOwnProperty('chat');
    const msg1Id = res.body._id;

    for (let uid = 1; uid < numUsers; uid++) {
      res = await request(app).put('/v1/message/reaction').set('authorization', uid).send({
        messageId: msg1Id,
        reaction: '❤️',
      });
      expect(res.status).to.equal(200);
      console.log('after reaction: ', res.body);

      //each reaction should notified to user0
      reset();
      resetTime = Date.now();
      await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
      console.log('notifs: ', notifs);
      expect(notifs.recent.token).to.equal('token0');
      expect(notifs.recent.notification.title).to.equal(`name${uid} reacted to your message`);
      expect(notifs.recent.notification.body).to.equal('❤️');
    }

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessageReaction.sender).to.equal('3');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(3);

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(3);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[1].sender).to.equal('2');
    expect(msg1.reactions[2].sender).to.equal('3');
    expect(msg1.reactions[0].reaction).to.equal('❤️');
    expect(msg1.reactions[1].reaction).to.equal('❤️');
    expect(msg1.reactions[2].reaction).to.equal('❤️');

    //user0 put reaction on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 0).send({
      messageId: msg1Id,
      reaction: '❤️',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //no notification should sent due of sender is message owner
    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessageReaction.sender).to.equal('0');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('❤️');
    expect(res.body[0].numUnreadMessages).to.equal(0);

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(4);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[1].sender).to.equal('2');
    expect(msg1.reactions[2].sender).to.equal('3');
    expect(msg1.reactions[3].sender).to.equal('0');
    expect(msg1.reactions[0].reaction).to.equal('❤️');
    expect(msg1.reactions[1].reaction).to.equal('❤️');
    expect(msg1.reactions[2].reaction).to.equal('❤️');
    expect(msg1.reactions[3].reaction).to.equal('❤️');

    //user0 put another reaction on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 0).send({
      messageId: msg1Id,
      reaction: '😎',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    //no notification should sent due of sender is message owner
    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    console.log('notifs: ', notifs);
    expect(notifs.numSent).to.equal(0);

    //user1 put another reaction on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😆',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    expect(notifs.recent.token).to.equal('token0');
    expect(notifs.recent.notification.title).to.equal(`name1 reacted to your message`);
    expect(notifs.recent.notification.body).to.equal('😆');

    //user0 open chat page
    res = await request(app).get('/v1/chat').set('authorization', 0);
    expect(res.status).to.equal(200);
    expect(res.body.length).to.equal(4);
    console.log('user0 chat page: ', res.body);
    expect(res.body[0].lastMessage.text).to.equal('msg1');
    expect(res.body[0].lastMessageReaction.sender).to.equal('1');
    expect(res.body[0].lastMessageReaction.reaction).to.equal('😆');
    expect(res.body[0].numUnreadMessages).to.equal(1);

    //user0 open the chat
    res = await request(app).get('/v1/message').set('authorization', 0).query({ chatId });
    expect(res.status).to.equal(200);
    console.log('chat messages: ', JSON.stringify(res.body, null, 2));
    expect(res.body.length).to.equal(2);

    msg1 = res.body.find((msg) => msg._id === msg1Id);
    console.log('msg1: ', msg1);
    expect(msg1.reactions.length).to.equal(6);
    expect(msg1.reactions[0].sender).to.equal('1');
    expect(msg1.reactions[1].sender).to.equal('2');
    expect(msg1.reactions[2].sender).to.equal('3');
    expect(msg1.reactions[3].sender).to.equal('0');
    expect(msg1.reactions[4].sender).to.equal('0');
    expect(msg1.reactions[5].sender).to.equal('1');
    expect(msg1.reactions[0].reaction).to.equal('❤️');
    expect(msg1.reactions[1].reaction).to.equal('❤️');
    expect(msg1.reactions[2].reaction).to.equal('❤️');
    expect(msg1.reactions[3].reaction).to.equal('❤️');
    expect(msg1.reactions[4].reaction).to.equal('😎');
    expect(msg1.reactions[5].reaction).to.equal('😆');

    // user 0 leaves group chat
    res = await request(app).patch('/v1/chat/leaveGroupChat').set('authorization', 0).send({ chatId });
    expect(res.status).to.equal(200);

    //user1 put another reaction on msg1
    res = await request(app).put('/v1/message/reaction').set('authorization', 1).send({
      messageId: msg1Id,
      reaction: '😱',
    });
    expect(res.status).to.equal(200);
    console.log('after reaction: ', res.body);

    reset();
    resetTime = Date.now();
    await waitFor((_) => notifs.numSent == 1 || Date.now() - resetTime > 100);
    //no notications should sent since user 0 was leave group
    expect(notifs.numSent).to.equal(0);
  });
});

it('enforce profile verification', async () => {
  constants.enforceVerification.restore();
  sinon.stub(constants, 'enforceVerification').returns(true);

  res = await request(app).put('/v1/user/initApp').set('authorization', 0).send({ locale: 'en' });
  expect(res.status).to.equal(200);
  res = await request(app).put('/v1/user/initApp').set('authorization', 1).send({ locale: 'es' });
  expect(res.status).to.equal(200);

  res = await request(app).post('/v1/user/picture/v2').set('authorization', 0).attach('image', validImagePath);
  expect(res.status).to.equal(200);

  res = await request(app).patch('/v1/user/sendLike').set('authorization', 0).send({
    user: '1',
  });
  expect(res.status).to.equal(200);
  res = await request(app).patch('/v1/user/approve').set('authorization', 1).send({
    user: '0',
  });
  expect(res.status).to.equal(200);

  // unverified - restricted
  res = await request(app).post('/v1/message').set('authorization', 0).send({
    user: '1',
    text: 'msg1',
  });
  expect(res.status).to.equal(403);
  expect(res.error.text).to.equal('Verify your profile in order to use this feature.');

  res = await request(app).post('/v1/message').set('authorization', 1).send({
    user: '0',
    text: 'msg1',
  });
  expect(res.status).to.equal(403);
  expect(res.error.text).to.equal('Verifica tu perfil para usar esta función.');

  const payload = {
    img: 'base-64-image-data',
    secure: {
      version: '2.4.0',
      token: 'token-data',
      verification: 'verification-data',
      signature: 'signature-data',
    },
  };

  res = await request(app).post('/v1/user/profileVerificationPicture/liveness').set('authorization', 0).send(payload);
  expect(res.status).to.equal(200);

  await new Promise((r) => setTimeout(r, 100));

  res = await request(app).put('/v1/user/initApp').set('authorization', 0);
  expect(res.status).to.equal(200);
  expect(res.body.user.verified).to.equal(true);
  expect(res.body.user.verificationStatus).to.equal('verified');

  // verified - not restricted
  res = await request(app).post('/v1/message').set('authorization', 0).send({
    user: '1',
    text: 'msg1',
  });
  expect(res.status).to.equal(200);
});
