const mongoose = require("mongoose");
const { taskStatus, suggestionType, batchStatus } = require("../lib/ai-image/const")

const AIImageSchema = new mongoose.Schema({
  user: { type: String, ref: "User" },
  createdAt: { type: Date, default: () => new Date() },
  originalImage: { type: String },
  suggestion: {
    type: {type: String, enum: [suggestionType.TEXT, suggestionType.VISUAL]},
    id:{ type: String },
    prompt:  { type: String },
  },
  userPrompt: { type: String },
  translationError: { type: String },
  fullPrompt: { type: String },
  serverId: { type: String },
  results: [
    {
      _id: false,
      id: { type: String },
      messageId: { type: String },
      status: { type: String, enum: [taskStatus.PROCESS, taskStatus.FAILURE, taskStatus.FINISHED], default: () => taskStatus.PROCESS },
      error: { type: String },
      imageUrl: { type: String },
      createdAt: { type: Date, default: () => new Date() },
      updatedAt: { type: Date, default: () => new Date() }
    }
  ],
  batchStatus: { type: String, enum:[batchStatus.DONE, batchStatus.FAILURE, batchStatus.EXPIRED, batchStatus.PENDING, batchStatus.QUEUE, batchStatus.SELECTED, batchStatus.REGENERATED] ,default: () => batchStatus.QUEUE}
});

AIImageSchema.index({
  user: 1,
  createdAt: 1,
});

module.exports = mongoose.model("AIImage", AIImageSchema);
