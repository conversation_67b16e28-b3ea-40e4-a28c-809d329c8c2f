const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');
const { validMbti } = require('../lib/personality');
const { enneagrams } = require('../lib/enneagram');
const { horoscopes } = require('../lib/horoscope');

function arrToObj(arr) {
  return arr.reduce((obj, val) => ({ ...obj, [val]: { type: Number } }), {});
}

const subcategorySchema = new mongoose.Schema({
  id: { type: Number, unique: true },
  name: { type: String },
  slug: { type: String },
  category: { type: Number },
  sort: { type: Number, default: Math.random },
  numProfiles: { type: Number },
  personalityCount:{
    mbti: arrToObj(validMbti),
    enneagram: arrToObj(enneagrams),
    horoscope: arrToObj(horoscopes),
  },
  countries: [{ type: String }],
  translatedLanguages: [ { type: String } ],
  intros: { type: mongoose.Mixed },
  linkedPillarKeywords: { type: mongoose.Mixed },
});

subcategorySchema.index({
  category: 1,
  sort: -1,
});

subcategorySchema.index({
  slug: 1,
});

// Define methods
// =============================================================================

// Export schema
// =============================================================================
let conn = connectionLib.getPersonalityDatabaseConnection() || mongoose;
module.exports = conn.model('Subcategory', subcategorySchema);
