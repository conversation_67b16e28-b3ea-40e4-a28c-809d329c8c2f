const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const aiNotificationSchema = new mongoose.Schema(
  {
    user: { type: String, ref: 'User' },
    primaryTopic: { type: String },
    alternativeTopic: { type: String, default: undefined },
    isAlternative: { type: Boolean, default: undefined },
    analyticsLabel: { type: String },
    prompt: { type: String, trim: true },
    output: { type: String, trim: true },
    isError: { type: Boolean, default: undefined },
    errorMessage: { type: String, default: undefined },
    cost: { type: Number, default: 0 },
    openAiResponse: { type: String, default: undefined },
  },
  {
    timestamps: true,
  },
);

aiNotificationSchema.index({ user: 1 });
aiNotificationSchema.index({ createdAt: 1 });

// Export schema
const connection = connectionLib.getEventsConnection() || mongoose;
module.exports = connection.model('AiNotification', aiNotificationSchema);
