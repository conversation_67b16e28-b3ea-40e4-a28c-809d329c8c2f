const mongoose = require('mongoose');
const { renameEthnicity } = require('../lib/rename-ethnicity');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/test';

const oldName = 'Berber';
const newName = 'Amazigh';

(async () => {
  await mongoose.connect(
    MONGODB_URI,
  );
  console.log('Connected to database');

  await renameEthnicity(oldName, newName);

  await mongoose.disconnect();
})();
