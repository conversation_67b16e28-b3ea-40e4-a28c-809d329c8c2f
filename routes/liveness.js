const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const moment = require('moment');
const httpErrors = require('../lib/http-errors');
const User = require('../models/user');
const LivenessChallenge = require('../models/liveness-challenge');
const { lambda } = require('../lib/lambda');
const s3 = require('../lib/s3');
const { updateUserScore } = require('../lib/score');
const { addProfilePicture } = require('../lib/liveness');
const { setFaceComparisonReferenceImage, verifyProfilePicture } = require('../lib/verification');
const socketLib = require('../lib/socket');
const { findBannedFace } = require('../lib/rekognition');
const reportLib = require('../lib/report');
const userLib = require('../lib/user');
const { updateAppMessage } = require('../middleware/user');
const { translate } = require('../lib/translate');
const constants = require('../lib/constants');

async function checkLivenessUploadAllowed(req, res, next) {
  if (!req.query.challengeId) {
    return next(httpErrors.forbiddenError());
  }
  const challenge = await LivenessChallenge.findOne({ id: req.query.challengeId } );
  if (!challenge) {
    return next(httpErrors.forbiddenError());
  }
  if (challenge.frames.length > 20) {
    return next(httpErrors.forbiddenError());
  }
  req.challenge = challenge;
  return next();
}


module.exports = function () {

  router.post('/start', asyncHandler(async (req, res, next) => {
    const payload = JSON.stringify({
      route: 'start',
      imageWidth: req.body.imageWidth,
      imageHeight: req.body.imageHeight,
    });
    const params = {
      FunctionName: 'liveness-detection',
      Payload: payload,
    };
    const data = await lambda.invoke(params).promise();
    const challengeParams = JSON.parse(data.Payload);
    const challenge = new LivenessChallenge(challengeParams);
    challenge.user = req.user._id;
    await challenge.save();
    res.json({
      challenge: challengeParams,
    });
  }));

  router.post('/frame', asyncHandler(checkLivenessUploadAllowed), s3.uploadLivenessImage.single('image'), asyncHandler(async (req, res, next) => {
    if (req.fileValidationError) {
      return next(req.fileValidationError);
    }
    if (!req.file) {
      return next(invalidInputError('Image was not provided'));
    }
    console.log(req.file);

    const challenge = req.challenge;
    challenge.frames.push({
      timestamp: Date.now(),
      key: req.file.key,
    });
    await challenge.save();
    res.json({});
  }));

  router.post('/verify', asyncHandler(async (req, res, next) => {
    const user = req.user;
    const challenge = await LivenessChallenge.findOne({ id: req.body.challengeId });
    if (!challenge
      || challenge.frames.length < 2
      || moment().diff(challenge.date, 'seconds') > 60
      || challenge.livenessSuccess !== undefined) {
      return next(httpErrors.forbiddenError());
    }

    user.verification.method = 'nose';
    if (!user.events.finished_signup) {
      user.verification.attemptedVerificationDuringSignup = true;
    }
    await user.save();

    /*
    // nose verification is disabled
    await user.setVerificationStatus('rejected');
    user.verification.verifiedBy = null;
    user.verification.verifiedDate = Date.now();
    user.verification.rejectionReason = updateAppMessage;
    challenge.rejectionReason = 'Nose challenge is disabled.';
    challenge.livenessSuccess = null;
    challenge.livenessFailureReason = null;
    await challenge.save();
    user.livenessVerification = challenge;
    await user.save();
    return res.json({
      verificationStatus: user.verification.status,
      rejectionReason: translate(user.verification.rejectionReason, user.locale),
    });
    */

    if (user.shadowBanned && !reportLib.eligibleForVerification(user.bannedReason)) {
      await user.setVerificationStatus('rejected', 'automated rejection due to shadow ban');
      user.verification.verifiedBy = null;
      user.verification.verifiedDate = Date.now();
      user.verification.rejectionReason = 'Nose challenge failed.';
      challenge.rejectionReason = 'User is banned.';
      challenge.livenessSuccess = null;
      challenge.livenessFailureReason = null;
      await challenge.save();
      user.livenessVerification = challenge;
      await user.save();
      return res.json({
        verificationStatus: user.verification.status,
        rejectionReason: user.verification.rejectionReason,
      });
    }

    if (user.isVerified()) {
      // ignore if user is already verified
      return res.json({
        verificationStatus: 'verified',
      });
    }

    const payload = JSON.stringify({
      route: 'verify',
      challenge,
      bucket_name: s3.AWS_S3_BUCKET,
    });
    const params = {
      FunctionName: 'liveness-detection',
      Payload: payload,
    };
    const data = await lambda.invoke(params).promise();
    const response = JSON.parse(data.Payload);
    challenge.livenessSuccess = response.success;
    challenge.livenessFailureReason = response.failure_reason;
    await challenge.save();

    setFaceComparisonReferenceImage(user, challenge.frames[0].key);
    if (response.success) {
      if (userLib.useNewReverification(user)) {
        const result = await verifyProfilePicture(user);
        if (result == 'verify') {
          await userLib.updateVerificationStatus(user, 'verified', undefined, 'passed nose challenge');
          user.verification.verifiedBy = null;
          user.verification.verifiedDate = Date.now();
          user.verification.newReverificationSystem = userLib.useNewReverification(user);
          challenge.compareFacesSuccess = true;
        } else if (result == 'manual') {
          await user.setVerificationStatus('pending', 'passed nose challenge but face comparison could not be performed');
          challenge.rejectionReason = 'Automated face comparison could not be performed.';
        } else {
          await user.setVerificationStatus('rejected', 'passed nose challenge but failed face comparison');
          user.verification.verifiedBy = null;
          user.verification.verifiedDate = Date.now();
          user.verification.rejectionReason = 'Make sure your first profile picture is a picture of you.';
          challenge.rejectionReason = 'Verification does not match profile pictures.';
          challenge.compareFacesSuccess = false;
        }
      } else {
        for (const profilePicture of user.pictures) {
          await addProfilePicture(user, challenge, profilePicture);
        }
        if (challenge.matchingPictures.length > 0) {
          if (challenge.notMatchingPictures.length == 0) {
            await userLib.updateVerificationStatus(user, 'verified', undefined, 'passed nose challenge');
            user.verification.verifiedBy = null;
            user.verification.verifiedDate = Date.now();
            challenge.compareFacesSuccess = true;
          } else {
            await user.setVerificationStatus('pending', 'passed nose challenge but some profile pictures contain a different face');
            challenge.rejectionReason = 'Some profile pictures contain a different face.';
          }
        } else {
          await user.setVerificationStatus('rejected', 'passed nose challenge but failed face comparison');
          user.verification.verifiedBy = null;
          user.verification.verifiedDate = Date.now();
          user.verification.rejectionReason = 'Verification does not match profile pictures.';
          challenge.rejectionReason = 'Verification does not match profile pictures.';
          challenge.compareFacesSuccess = false;
        }
      }
    } else {
      await user.setVerificationStatus('rejected', 'failed nose challenge');
      user.verification.verifiedBy = null;
      user.verification.verifiedDate = Date.now();
      user.verification.rejectionReason = 'Nose challenge failed.';
      challenge.rejectionReason = 'Nose challenge failed.';
    }
    await challenge.save();
    user.livenessVerification = challenge;
    await user.save();
    if (user.verification.status == 'verified') {
      await socketLib.grantVerifyProfileAward(user);
    }

    if (user.livenessVerification.frames.length > 0) {
      const bannedFace = await findBannedFace(user.livenessVerification.frames[0].key);
      if (bannedFace) {
        await reportLib.banDueToBannedFaceFound(req.user, 'liveness verification picture', user.livenessVerification.frames[0].key, bannedFace.Face.ExternalImageId);
      }
    }

    res.json({
      verificationStatus: user.verification.status,
      rejectionReason: user.verification.rejectionReason,
    });
  }));

  router.post('/requestManualVerification', asyncHandler(async (req, res, next) => {
    const user = req.user;
    if (!user.livenessVerification
      || user.livenessVerification.id != req.body.challengeId
      || user.livenessVerification.livenessSuccess === undefined
      || user.livenessVerification.manuallyCheckedResult !== undefined) {
      return next(httpErrors.forbiddenError());
    }

    if (user.isVerified()) {
      // ignore if user is already verified
      return res.json({});
    }

    await user.setVerificationStatus('pending', 'user requested manual verification for nose challenge');
    await user.save();
    res.json({});
  }));

  return router;
};
