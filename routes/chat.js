const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const cmp = require('semver-compare');
const mongoose = require('mongoose');
const moment = require('moment');
const cfsign = require('aws-cloudfront-sign');
const admin = require('../config/firebase-admin');
const premiumLib = require('../lib/premium');
const { notFoundError, invalidInputError, forbiddenError, applicationError } = require('../lib/http-errors');
const User = require('../models/user');
const Chat = require('../models/chat');
const Message = require('../models/message');
const { findUser, deprecated } = require('../middleware/user');
const {
  findChatIfExists, findChat, findChatPopulate, findApprovedChat,
} = require('../middleware/chat');
const personalityLib = require('../lib/personality');
const { getSigningParams } = require('../lib/cloudfront');
const chatLib = require('../lib/chat');
const locationLib = require('../lib/location');
const { pageSize } = require('../lib/constants');
const { hasDuplicates } = require('../lib/basic');
const { formatMessage } = require('../lib/message.js');
const { sendSocketEvent } = require('../lib/socket');
const { translate } = require('../lib/translate');
const ChatExportHistory = require('../models/chat-export-history');
const TrackAutoreponseUsage = require('../models/track-autoresponse-usage');

const groupChatSizeErrMsg = 'There is a maximum number of 8 people in a group chat.';

async function parseBeforeParam(req) {
  let beforeChat;
  let beforeDate;

  if (req.query.beforeId) {
    beforeChat = await Chat.findById(req.query.beforeId);
    if (!beforeChat) {
      return { err: true };
    }
    beforeDate = beforeChat.lastMessageTime;
  } else if (req.query.before && Date.parse(req.query.before)) {
    beforeDate = req.query.before;
  }

  return {
    beforeChat,
    beforeDate,
  };
}

module.exports = function () {
  router.get('/pending', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate, err } = await parseBeforeParam(req);
    if (err) {
      return res.json({ chats: [] });
    }
    const chats = await chatLib.getPendingChats(req.user, beforeDate);
    res.json({ chats });
  }));

  router.get('/approved', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate, err } = await parseBeforeParam(req);
    if (err) {
      return res.json({ chats: [] });
    }
    const chats = await chatLib.getApprovedChats(req.user, beforeDate, beforeChat, req.query.pinned);
    res.json({ chats });
  }));

  router.get('/sent', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate, err } = await parseBeforeParam(req);
    if (err) {
      return res.json({ chats: [] });
    }
    const chats = await chatLib.getSentChats(req.user, beforeDate);
    res.json({ chats });
  }));

  router.get('/messaged', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate, err } = await parseBeforeParam(req);
    if (err) {
      return res.json({ chats: [] });
    }
    const chats = await chatLib.getMessagedChats(req.user, beforeDate, beforeChat, req.query.pinned);
    res.json({ chats });
  }));

  router.get('/sortMessages', asyncHandler(async (req, res, next) => {
    let { sort = 'recent', paginationToken } = req.query;
    let { pinned, beforeDate, chatType = true, err } = chatLib.decodePaginationToken(paginationToken)
    if(!paginationToken) {
      pinned = true
    } 
    if (err) {
      return res.json({ chats: [], paginationToken: null });
    }
    const { chats, token } = await chatLib.getMessagedSortedChats(req.user, beforeDate, pinned, sort, chatType)
    res.json({ 
      chats,
      paginationToken: token,
    });
  }));

  router.get('/notMessaged', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate, err } = await parseBeforeParam(req);
    if (err) {
      return res.json({ chats: [] });
    }
    const chats = await chatLib.getNotMessagedChats(req.user, beforeDate);
    res.json({ chats });
  }));

  router.get('/allContacts', asyncHandler(async (req, res, next) => {
    const chats = await chatLib.getContacts(req.user);
    res.json({ chats });
  }));

  router.get('/contacts', asyncHandler(async (req, res, next) => {
    const chats = await chatLib.getContacts(req.user, req.query.sort, req.query.beforeId);
    res.json({ chats });
  }));

  router.get('/', asyncHandler(async (req, res, next) => {
    const { beforeChat, beforeDate } = await parseBeforeParam(req);
    const approvedChats = await chatLib.getApprovedChats(req.user, beforeDate);
    const pendingChats = await chatLib.getPendingChats(req.user, beforeDate);
    const chats = approvedChats.concat(pendingChats);
    res.json(chats);
  }));

  router.get('/individualChat', asyncHandler(findChatPopulate), asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { chat } = req;
    const formattedChat = chatLib.formatChat(chat.toObject({ flattenMaps: true }), user);
    res.json({
      chat: formattedChat,
    });
  }));

  router.get('/readReceipt', asyncHandler(findChat), asyncHandler(async (req, res, next) => {
    const { chat } = req;
    const receivingUser = req.user;

    let partnerNumUnreadMessages;
    if (premiumLib.isPremiumV1OrGodMode(receivingUser) && !chat.groupChat) {
      const partnerId = chat.users.find((x) => x != receivingUser._id);
      const partner = await User.findById(partnerId);
      if (!(premiumLib.isPremiumV1OrGodMode(partner) && partner.hideReadReceipts)) {
        const readReceipt = chat.readReceipts.get(partnerId);
        if (readReceipt) {
          partnerNumUnreadMessages = readReceipt.numUnreadMessages;
        }
      }
    }

    res.json({
      partnerNumUnreadMessages,
    });
  }));

  router.patch('/pin', asyncHandler(findApprovedChat), asyncHandler(async (req, res, next) => {
    const userId = req.uid;
    const chatId = req.chat._id;
    await Chat.updateOne(
      { _id: chatId, users: userId, pinned: { $ne: userId } },
      { $push: { pinned: userId } },
    );
    res.json({});
  }));

  router.patch('/unpin', asyncHandler(findApprovedChat), asyncHandler(async (req, res, next) => {
    const userId = req.uid;
    const chatId = req.chat._id;
    await Chat.updateOne(
      { _id: chatId, users: userId, pinned: userId },
      { $pull: { pinned: userId } },
    );
    res.json({});
  }));

  router.patch('/hide', asyncHandler(findApprovedChat), asyncHandler(async (req, res, next) => {
    const userId = req.uid;
    const chatId = req.chat._id;
    await Chat.updateOne(
      { _id: chatId, users: userId, hidden: { $ne: userId } },
      { $push: { hidden: userId } },
    );
    res.json({});
  }));

  router.patch('/markUnread', asyncHandler(findApprovedChat), asyncHandler(async (req, res, next) => {
    const { chat } = req;
    chatLib.incrementUnreadMessages(chat, req.uid);
    if(chat.perUserState){
      const userState = chat.perUserState.find(state => state.userId === req.uid);
      if (userState) {
        userState.unread = true; // This modifies the object in the array of perUserState
      }    
    }
    await chat.save();
    res.json({});
  }));

  router.get('/userProfile', deprecated, (req, res, next) => next(notFoundError()));

  router.patch('/createGroupChat', asyncHandler(async (req, res, next) => {
    // validate input
    const { user } = req;
    if (user.shadowBanned) {
      return next(forbiddenError());
    }

    const { groupChatName } = req.body;
    if (groupChatName) {
      if (typeof groupChatName !== 'string' || groupChatName.length > 500) {
        return next(invalidInputError());
      }
    }

    let otherUserIds = req.body.users;
    if (!Array.isArray(otherUserIds)
      || hasDuplicates(otherUserIds)
    ) {
      return next(invalidInputError());
    }
    otherUserIds = otherUserIds.filter((id) => id != user._id);
    if (otherUserIds.length < 1 || otherUserIds.length > 7) {
      return next(invalidInputError(groupChatSizeErrMsg));
    }

    // check other users exist, matched with current user, not banned
    const orArray = otherUserIds.map((id) => ({
      userIdHash: Chat.createUserIdHash([user._id, id]),
      groupChat: { $ne: true },

    }));
    const chats = await Chat.find({ $or: orArray, deletedAt: null });

    let otherUsers = await User.find({ _id: { $in: otherUserIds } });
    otherUsers = otherUsers.filter((u) => !u.banned && !u.shadowBanned
             && chats.some((e) => e.users.includes(u._id)));
    if (otherUsers.length == 0) {
      return next(notFoundError());
    }

    // create group chat
    const chat = new Chat({
      users: [user].concat(otherUsers),
      pendingUser: null,
      messaged: true,
      lastMessageTime: Date.now(),
      groupChat: true,
      groupChatName,
    });
    chat.readReceipts.set(user._id, { numUnreadMessages: 0 });
    for (const otherUser of otherUsers) {
      chat.readReceipts.set(otherUser._id, { numUnreadMessages: 1 });
    }

    const names = new Intl.ListFormat().format(otherUsers.map((x) => x.firstName));
    const msg = `${user.firstName} invited ${names} to a group chat.`;
    let message = new Message({
      createdAt: Date.now(),
      chat: chat._id,
      text: msg,
      sender: '',
      systemMessage: true,
    });
    await message.save();

    if (groupChatName) {
      message = new Message({
        createdAt: Date.now(),
        chat: chat._id,
        text: `${user.firstName} named the group ${groupChatName}.`,
        sender: '',
        systemMessage: true,
      });
      await message.save();
    }

    chat.lastMessage = message;
    await chat.save();

    // send notifications
    for (const otherUser of otherUsers) {
      const data = {
        _id: chat._id,
      };
      let notificationData = { approvedChat: JSON.stringify(data) };
      let notificationBody = translate('Added you to group chat', otherUser.locale);
      if (otherUser.supportsGroupChat()) {
        const formattedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          otherUser,
        );
        sendSocketEvent(otherUser._id, 'approved chat', formattedChat);
      } else {
        notificationData = undefined;
        notificationBody += ' [Update app to latest version to view]';
      }

      admin.sendNotification(
        otherUser,
        'matches',
        user.firstName,
        notificationBody,
        notificationData,
        null,
        'general',
        'added-to-group-chat',
      );
    }
    const formattedChat = chatLib.formatChat(
      chat.toObject({ flattenMaps: true }),
      user,
    );
    res.json({
      chat: formattedChat,
    });
  }));

  router.patch('/leaveGroupChat', asyncHandler(async (req, res, next) => {
    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }

    const msg = `${req.user.firstName} left the chat.`;
    const message = await Chat.removeFromGroupChat(
      chatId,
      req.uid,
      msg,
    );

    if (!message) {
      return res.json({});
    }

    // send notifications and socket events to users who are left
    const chat = await Chat
      .findById(chatId)
      .populate('users')
      .populate('lastMessage');

    for (const otherUser of chat.users) {
      if (otherUser._id == req.user._id) {
        continue;
      }
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }
    await chat.save();

    for (const otherUser of chat.users) {
      const formattedMessage = formatMessage(message, null, otherUser);
      sendSocketEvent(otherUser._id, 'message', formattedMessage);

      if (otherUser._id == req.user._id) {
        continue;
      }

      const data = {
        _id: chat._id,
      };
      let notificationData = { approvedChat: JSON.stringify(data) };
      const title = chat.groupChatName ? chat.groupChatName : translate('Group Chat', otherUser.locale);
      let notificationBody = formattedMessage.text;
      const notificationCategory = 'messages';

      if (otherUser.supportsGroupChat()) {
        const formattedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          otherUser,
        );
        sendSocketEvent(otherUser._id, 'approved chat', formattedChat);
      } else {
        notificationData = undefined;
        notificationBody += ' [Update app to latest version to view]';
      }

      if (chat.muted.includes(otherUser._id)) {
        continue;
      }

      admin.sendNotification(
        otherUser,
        notificationCategory,
        title,
        notificationBody,
        notificationData,
        null,
        'general',
        'group-chat-system-message',
      );
    }

    res.json({});
  }));

  router.patch('/mute', asyncHandler(async (req, res, next) => {
    const userId = req.uid;
    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }

    const chat = await Chat.findById(req.body.chatId);
    if (!chat || !chat.users.includes(req.uid)) {
      return next(notFoundError());
    }

    await Chat.updateOne(
      { _id: chatId, users: userId },
      { $push: { muted: userId } },
    );

    res.json({});
  }));

  router.patch('/unmute', asyncHandler(async (req, res, next) => {
    const userId = req.uid;
    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }

    const chat = await Chat.findById(req.body.chatId);
    if (!chat || !chat.users.includes(req.uid)) {
      return next(notFoundError());
    }

    await Chat.updateOne(
      { _id: chatId, users: userId },
      { $pull: { muted: userId } },
    );

    res.json({});
  }));

  router.patch('/inviteToGroupChat', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }

    let chat = await Chat.findById(chatId);
    if (!chat || !chat.groupChat || !chat.users.includes(req.uid)) {
      return next(notFoundError());
    }

    let otherUserIds = req.body.users;
    if (!Array.isArray(otherUserIds)
      || hasDuplicates(otherUserIds)
    ) {
      return next(invalidInputError());
    }
    otherUserIds = otherUserIds.filter((id) => id != user._id);
    if (otherUserIds.length < 1 || otherUserIds.length > 7) {
      return next(invalidInputError(groupChatSizeErrMsg));
    }
    if (chat.users.length + otherUserIds.length > 8) {
      return next(invalidInputError(groupChatSizeErrMsg));
    }

    // check other users exist, matched with current user, not banned
    const orArray = otherUserIds.map((id) => ({
      userIdHash: Chat.createUserIdHash([user._id, id]),
      groupChat: { $ne: true },
    }));
    const chats = await Chat.find({ $or: orArray, deletedAt: null });

    let otherUsers = await User.find({ _id: { $in: otherUserIds } });
    otherUsers = otherUsers.filter((u) => !u.banned && !u.shadowBanned
             && chats.some((e) => e.users.includes(u._id)));
    if (otherUsers.length == 0) {
      return next(notFoundError());
    }

    const addedUsers = [];
    for (const otherUser of otherUsers) {
      const res = await Chat.updateOne(
        { _id: chatId, users: { $ne: otherUser._id } },
        { $push: { users: otherUser._id } },
      );
      if (res.modifiedCount) {
        addedUsers.push(otherUser);
      }
    }

    if (addedUsers.length > 0) {
      const names = new Intl.ListFormat().format(addedUsers.map((x) => x.firstName));
      const msg = `${req.user.firstName} invited ${names} to the chat.`;
      const message = new Message({
        createdAt: Date.now(),
        chat: chatId,
        text: msg,
        sender: '',
        systemMessage: true,
      });
      await message.save();
      chat.lastMessage = message;
      chat.lastMessageTime = Date.now();
      await chat.save();

      // send notifications and socket events to existing and added users
      chat = await Chat
        .findById(chatId)
        .populate('users')
        .populate('lastMessage');

      for (const otherUser of chat.users) {
        if (otherUser._id == req.user._id) {
          continue;
        }
        chatLib.incrementUnreadMessages(chat, otherUser._id);
      }
      await chat.save();

      for (const otherUser of chat.users) {
        const formattedMessage = formatMessage(message, null, otherUser);
        sendSocketEvent(otherUser._id, 'message', formattedMessage);

        if (otherUser._id == user._id) {
          continue;
        }

        const data = {
          _id: chat._id,
        };
        let notificationData = { approvedChat: JSON.stringify(data) };
        let title = req.user.firstName;
        let notificationBody = translate('Added you to group chat', otherUser.locale);
        let notificationCategory = 'matches';
        let analyticsLabel = 'added-to-group-chat';

        if (!addedUsers.some((x) => x._id == otherUser._id)) {
          title = chat.groupChatName ? chat.groupChatName : translate('Group Chat', otherUser.locale);
          notificationBody = formattedMessage.text;
          notificationCategory = 'messages';
          analyticsLabel = 'group-chat-system-message';
        }

        if (otherUser.supportsGroupChat()) {
          const formattedChat = chatLib.formatChat(
            chat.toObject({ flattenMaps: true }),
            otherUser,
          );
          sendSocketEvent(otherUser._id, 'approved chat', formattedChat);
        } else {
          notificationData = undefined;
          notificationBody += ' [Update app to latest version to view]';
        }

        if (chat.muted.includes(otherUser._id)) {
          continue;
        }

        admin.sendNotification(
          otherUser,
          notificationCategory,
          title,
          notificationBody,
          notificationData,
          null,
          'general',
          analyticsLabel,
        );
      }
    }

    res.json({});
  }));

  router.patch('/removeFromGroupChat', asyncHandler(async (req, res, next) => {
    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }
    let chat = await Chat.findById(req.body.chatId);
    if (!chat || !chat.groupChat || !chat.users.includes(req.uid)) {
      return next(notFoundError());
    }

    const userId = req.body.user;
    if (!userId || typeof userId !== 'string') {
      return next(invalidInputError());
    }
    const otherUser = await User.findById(userId);
    if (!otherUser) {
      return next(notFoundError());
    }

    const msg = `${req.user.firstName} removed ${otherUser.firstName} from the chat.`;
    const message = await Chat.removeFromGroupChat(
      chatId,
      userId,
      msg,
    );

    if (!message) {
      return res.json({});
    }

    // send notifications and socket events to user who got removed
    const title = chat.groupChatName ? chat.groupChatName : translate('Group Chat', otherUser.locale);
    admin.sendNotification(
      otherUser,
      'messages',
      title,
      translate('{{name}} removed you from the chat.', otherUser.locale, { name: req.user.firstName }),
      null,
      null,
      'general',
      'group-chat-system-message',
    );
    sendSocketEvent(otherUser._id, 'deleted chat', { _id: chatId });

    // send notifications and socket events to other users who are left
    chat = await Chat
      .findById(chatId)
      .populate('users')
      .populate('lastMessage');

    for (const otherUser of chat.users) {
      if (otherUser._id == req.user._id) {
        continue;
      }
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }
    await chat.save();

    for (const otherUser of chat.users) {
      const formattedMessage = formatMessage(message, null, otherUser);
      sendSocketEvent(otherUser._id, 'message', formattedMessage);

      if (otherUser._id == req.user._id) {
        continue;
      }

      const data = {
        _id: chat._id,
      };
      let notificationData = { approvedChat: JSON.stringify(data) };
      let notificationBody = formattedMessage.text;
      const notificationCategory = 'messages';

      if (otherUser.supportsGroupChat()) {
        const formattedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          otherUser,
        );
        sendSocketEvent(otherUser._id, 'approved chat', formattedChat);
      } else {
        notificationData = undefined;
        notificationBody += ' [Update app to latest version to view]';
      }

      if (chat.muted.includes(otherUser._id)) {
        continue;
      }

      admin.sendNotification(
        otherUser,
        notificationCategory,
        title,
        notificationBody,
        notificationData,
        null,
        'general',
        'group-chat-system-message',
      );
    }

    res.json({});
  }));

  router.patch('/changeGroupChatName', asyncHandler(async (req, res, next) => {
    const { groupChatName } = req.body;
    if (typeof groupChatName !== 'string' || groupChatName.length > 500) {
      return next(invalidInputError());
    }

    const { chatId } = req.body;
    if (!chatId || typeof chatId !== 'string' || !mongoose.isValidObjectId(chatId)) {
      return next(invalidInputError());
    }

    let chat = await Chat.findById(req.body.chatId);
    if (!chat || !chat.groupChat || !chat.users.includes(req.uid)) {
      return next(notFoundError());
    }

    const oldChatName = chat.groupChatName;
    const msg = groupChatName
      ? `${req.user.firstName} named the group ${groupChatName}.`
      : `${req.user.firstName} removed the group name.`;
    const message = new Message({
      createdAt: Date.now(),
      chat: chat._id,
      text: msg,
      sender: '',
      systemMessage: true,
    });
    await message.save();

    chat.lastMessage = message;
    chat.lastMessageTime = Date.now();
    chat.groupChatName = groupChatName;
    await chat.save();

    // send notifications and socket events to other users
    chat = await Chat
      .findById(chatId)
      .populate('users')
      .populate('lastMessage');

    for (const otherUser of chat.users) {
      if (otherUser._id == req.user._id) {
        continue;
      }
      chatLib.incrementUnreadMessages(chat, otherUser._id);
    }
    await chat.save();

    for (const otherUser of chat.users) {
      const formattedMessage = formatMessage(message, null, otherUser);
      sendSocketEvent(otherUser._id, 'message', formattedMessage);

      if (otherUser._id == req.user._id) {
        continue;
      }

      const data = {
        _id: chat._id,
      };
      const title = oldChatName ? oldChatName : translate('Group Chat', otherUser.locale);
      let notificationData = { approvedChat: JSON.stringify(data) };
      let notificationBody = formattedMessage.text;
      const notificationCategory = 'messages';

      if (otherUser.supportsGroupChat()) {
        const formattedChat = chatLib.formatChat(
          chat.toObject({ flattenMaps: true }),
          otherUser,
        );
        sendSocketEvent(otherUser._id, 'approved chat', formattedChat);
      } else {
        notificationData = undefined;
        notificationBody += ' [Update app to latest version to view]';
      }

      if (chat.muted.includes(otherUser._id)) {
        continue;
      }

      admin.sendNotification(
        otherUser,
        notificationCategory,
        title,
        notificationBody,
        notificationData,
        null,
        'general',
        'group-chat-system-message',
      );
    }

    res.json({});
  }));

  router.post('/exportChat', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { chatId } = req.body;

    const chat = await Chat.findOne({ _id: chatId });
    if (!chat) {
      return next(notFoundError());
    }

    if (!chat.users.includes(user._id) || chat.pendingUser !== null || chat.groupChat || chat.deletedAt) {
      return next(forbiddenError(req.__('You are not authorized to export this chat.')));
    }

    let exportRequest = await ChatExportHistory.findOne({ chatId }).sort({ requestedAt: -1 });
    if (!exportRequest) {
      const newRequest = new ChatExportHistory({ chatId, requestedBy: user._id });
      await newRequest.save();
      return res.json({});
    }

    switch (exportRequest.status) {
    case 'approved':
      // If request is already approved, inform user to wait for completion
      return next(forbiddenError(req.__('You have already submitted a request for your data. You will receive a download link via email.')));
    case 'failed':
      // If previous request failed, creating new request
      await new ChatExportHistory({ chatId, requestedBy: user._id }).save();
      return res.json({});
    case 'pending':
      if (exportRequest.requestedBy === user._id) {
        return next(forbiddenError(req.__('To complete the download, your match will also need to submit a Download Chat request. Once both requests are submitted, you will both receive a download link via email.')));
      }
      // Approve the pending request if made by another user
      exportRequest.status = 'approved';
      await exportRequest.save();
      return res.json({});
    case 'completed':
      if (exportRequest.requestedAt > new Date(Date.now() - 24 * 60 * 60 * 1000)) {
        return next(forbiddenError(req.__('You have already completed exporting this chat within the last 24 hours.')));
      }
      await new ChatExportHistory({ chatId, requestedBy: user._id }).save();
      return res.json({});
    default:
      return next(applicationError());
    }
  }));

  router.patch('/leaveAutomatedChat', asyncHandler(async (req, res, next) => {
    const { user } = req;
    const { chatId } = req.body;

    if (user._id !== chatLib.BOO_SUPPORT_ID) {
      return next(invalidInputError());
    }

    const chat = await Chat.findById(chatId).populate('users');
    if (!chat || !(chat?.automatedChat && chat?.automatedChatState?.supportAdded)) {
      return next(notFoundError());
    }

    // Create system message
    const systemMessage = await Message.create({
      createdAt: Date.now(),
      chat: chat._id,
      text: `${user.firstName} left the chat.`,
      sender: '',
      systemMessage: true,
    });

    const otherUser = chat.users.find((x) => x._id.toString() !== user._id.toString() && x._id.toString() !== chatLib.BOO_BOT_ID);

    if (!chat.automatedChatState.rateSupportShownV2) {
      sendSocketEvent(otherUser._id, 'automatedChat session end', {});
      chat.automatedChatState.rateSupportShown = undefined;
      chat.automatedChatState.rateSupportShownV2 = true;
    }

    // Update the chat object
    Object.assign(chat, {
      lastMessage: systemMessage,
      lastMessageTime: Date.now(),
      users: chat.users.filter((x) => x._id.toString() !== user._id.toString()),
    });
    chat.automatedChatState.supportAdded = false;
    chat.incrementNumMessages();
    chatLib.incrementUnreadMessages(chat, user._id);
    await chat.save();

    // Notify other user in the chat
    const formattedChat = chatLib.formatChat(chat.toObject({ flattenMaps: true }), otherUser);
    sendSocketEvent(otherUser._id, 'support left automatedChat', formattedChat);
    sendSocketEvent(otherUser._id, 'message', formatMessage(systemMessage, null, otherUser));

    res.json({});

    // Resolve this flow in TrackAutoreponseUsage
    await TrackAutoreponseUsage.findOneAndUpdate(
      { user: otherUser._id, outcome: 'forwarded' },
      { outcome: 'resolved' },
      { sort: { _id: -1 } },
    );
  }));

  return router;
};
