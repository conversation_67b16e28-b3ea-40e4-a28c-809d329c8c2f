const User = require('../models/user');

async function renameEthnicity(oldName, newName) {
  const res = await User.updateMany(
    {
      $or: [
        { ethnicities: oldName },
        { 'preferences.ethnicities': oldName },
      ]
    },
    [
      {
        $set: {
          ethnicities: {
            $map: {
              input: "$ethnicities",
              in: {
                $cond: {
                  if: { $eq: [ '$$this', oldName ] },
                  then: newName,
                  else: '$$this',
                }
              },
            }
          },
          'preferences.ethnicities': {
            $map: {
              input: "$preferences.ethnicities",
              in: {
                $cond: {
                  if: { $eq: [ '$$this', oldName ] },
                  then: newName,
                  else: '$$this',
                }
              },
            }
          },
        }
      },
    ],
  );
  console.log(res);
}

module.exports = {
  renameEthnicity,
}
