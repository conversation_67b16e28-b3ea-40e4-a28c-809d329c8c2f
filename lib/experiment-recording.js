const ExperimentRecordingModel = require("../models/experiment-recording");
const { isPremium } = require("./premium");
const User = require("../models/user");

/**
 * Class representing an experiment recording.
 * The entry point to use this class is the `createAndSave` method.
 * The method requires 1 parameters:
 *
 * @param {string} userData - The data of the user, caller should query user data to DB and then send it as param.
 *
 * @returns {Object} - The return format includes a `recordingTrigger` object with the following structure:
 *
 * recordingTrigger: {
 *  record: {boolean}, // Flag for frontend to start recording
 *  metadata: {
 *    userPlatform: {string} 'android', 'ios',
 *    userDeviceSize: {string} 's', 'm', 'l',
 *    userGender: {string} 'male', 'female', 'non-binary',
 *    userAge: {string} '18-23', '24-33', '34+',
 *    userPreferences: {string} 'dating', 'friends', 'both',
 *    userSubscription: {string} 'premium', 'free',
 *    userID: {string},
 *    userLanguage: {string} (e.g., `en`),
 *    userConfig: {string} (e.g., `app_000=true`),
 *  }
 * }
 *
 * Possible return scenarios:
 * 1. **Failure:** The method can fail in the following cases:
 *  a. The userID was already recorded for another experiment.
 *  b. The quota was full for the audience combination.
 *  c. Missing required data during processing or validation.
 *  d. An error occurred during execution.
 *
 * Example of a failed return:
 * recordingTrigger: { record: false }
 *
 * 2. **Success:** The return format for a successful scenario depends on the value of `isOnBoarding`.
 *
 *  a. **isOnBoarding = false**
 *      Example return:
 *      recordingTrigger: {
 *        record: true,
 *        metadata: {
 *          userPlatform: 'ios',
 *          userDeviceSize: 's',
 *          userGender: 'male',
 *          userAge: '18-23',
 *          userPreferences: 'both',
 *          userSubscription: 'premium',
 *          userID: '4',
 *          userLanguage: 'en',
 *          userConfig: 'app_141=true'
 *        }
 *      }
 *
 *  b. **isOnBoarding = true**
 *      Example return:
 *      recordingTrigger: {
 *        record: true,
 *        metadata: {
 *          userPlatform: 'ios',
 *          userDeviceSize: 's',
 *          userID: '88',
 *          userConfig: 'app_141=true'
 *        }
 *      }
 *
 * --- CURRENTLY IT ONLY USED ON INIT APP ---
 * How to use:
 *  1. Determine which endpoint will be used as the trigger. Communicate with the frontend team to ensure they call the appropriate endpoint during the event marked as a trigger in the Jira ticket.
 *  2. Import `ExperimentRecording` into the endpoint file.
 *  3. Create the experiment config (e.g., `app_<JIRA TICKET NUMBER>`) on `/model/user.js`.
 *  4. Add experiment to the ExperimentList
 *  4. Call the `createAndSave` const recording = await ExperimentRecording.createAndSave(user);`
 *  5. Include the return value alongside the original endpoint return value.
 *
 */


class ExperimentRecording {
  static Gender = {
    MALE: "male",
    FEMALE: "female",
    NON_BINARY: "non-binary",
  };

  static Age = {
    RANGE_18_23: "18-23",
    RANGE_24_33: "24-33",
    RANGE_34_PLUS: "34+",
  };

  static Preferences = {
    DATING: "dating",
    FRIENDS: "friends",
    BOTH: "both",
  };

  static Subscription = {
    PAID: "premium",
    FREE: "free",
  };

  static Country = {
    US: "US",
    INDIA: "IN",
    BRAZIL: "BR",
    RANDOM: "Random",
  };

  //list of experiments that will trigger recording
  static ExperimentList = [
    // 1.13.79
    {
      experimentId: 'app_517',
      isOnBoarding: false,
      recordNonBinary: false,
    },
  ]

  static ResearchesList = [
    {
      experimentId: 'rsrch_onboarding_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_coinQuest_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_boost_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_superlove_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_premium_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_filter_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_filter_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_universes_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
    {
      experimentId: 'rsrch_match_1',
      isOnBoarding: true,
      recordNonBinary: true,
    },
  ]

  constructor(platform, deviceSize, country, experiment, userID) {
    if (!platform) throw new Error("Platform is required");
    if (!deviceSize) throw new Error("deviceSize is required");
    if (!country) throw new Error("Country is required");
    if (!experiment) throw new Error("Experiment is required");
    if (!userID) throw new Error("userID is required");

    this.platform = platform;
    this.deviceSize = deviceSize;
    this.country = this.#validateCountry(country);
    this.experiment = experiment;
    this.userID = userID;
    this.gender = null;
    this.age = null;
    this.preferences = null;
    this.subscription = null;
    this.language = null;
    this.audienceKey = null;
    this.isOnBoarding = false;
    this.recordNonBinary = false;
    this.trafficSource = 'organic';
    this.appVersion = null;
  }

  #validateSubscription(subscription) {
    if (
      subscription &&
      !Object.values(ExperimentRecording.Subscription).includes(subscription)
    ) {
      console.log(`Experiment recording - Invalid subscription: ${subscription}`);
    }
    return subscription || null;
  }

  #validateCountry(country) {
    if (
      country &&
      !Object.values(ExperimentRecording.Country).includes(country)
    ) {
      return "Random";
    }
    return country;
  }

  #validateGender(gender) {
    if (gender && !Object.values(ExperimentRecording.Gender).includes(gender)) {
      console.log(`Experiment recording - Invalid gender: ${gender}`);
    }
    return gender || null;
  }

  #validateAndAssignAge(age) {
    if (typeof age === "number") {
      return this.#assignAgeRange(age);
    } else if (age && Object.values(ExperimentRecording.Age).includes(age)) {
      return age;
    }
    console.log(`Experiment recording - Invalid age: ${age}`);
    return null

  }

  #assignAgeRange(age) {
    if (age >= 17 && age <= 23) return ExperimentRecording.Age.RANGE_18_23;
    if (age >= 24 && age <= 33) return ExperimentRecording.Age.RANGE_24_33;
    if (age >= 34) return ExperimentRecording.Age.RANGE_34_PLUS;
    console.log(`Experiment recording - Age out of valid range: ${age}`);
    return null
  }

  #validatePreferences(preferences) {
    let preference = "both";
    if (preferences.dating.length > 0 && preferences.friends.length > 0) {
      preference = "both";
    } else if (preferences.dating.length > 0) {
      preference = "dating";
    } else if (preferences.friends.length > 0) {
      preference = "friends";
    } else {
      console.log(`Experiment recording - Invalid preferences: ${preferences}`);
      return null
    }

    if (
      preferences &&
      !Object.values(ExperimentRecording.Preferences).includes(preference)
    ) {
      console.log(`Experiment recording - Invalid preferences: ${preferences}`);
      return null
    }
    return preference || null;
  }

  // Create a unique key for each audience combination
  #getAudienceKey() {
    if (
      this.gender &&
      this.age &&
      this.preferences &&
      this.language &&
      this.subscription
    ) {
      return `${this.platform}_${this.deviceSize}_${this.gender}_${this.age}_${this.preferences}_${this.country}_${this.experiment}`;
    } else {
      return `${this.platform}_${this.deviceSize}_${this.country}_${this.experiment}`;
    }
  }

  #getUXResearchAudienceKey() {
      return `${this.appVersion}_${this.language}_${this.experiment}`;
  }

  static #getUserExperiments(user) {
    // Get the user's config object
    const userConfig = user.config;

    // Filter the experiment list based on the user's config
    return ExperimentRecording.ExperimentList.filter(experiment => userConfig[experiment.experimentId]);
  }

  static #getResearchLists() {
    // Filter the experiment list based on the user's config
    return ExperimentRecording.ResearchesList;
  }

  static async #isUserEverRecorded(id){
    const existingRecord = await ExperimentRecordingModel.findOne({userID: id});
    console.log('existingRecord :', existingRecord)
    if (existingRecord) {
      console.log(
        `User ${id} has already been recorded for another experiment.`
      );
      return true
    }
    return false
  }

  // Static initialization method to create an instance with user data if applicable
  static async #initializeExperimentRecording(userData) {
    if (!userData) {
      console.log(`Experiment recording - user data is required. Aborting save.`);
      return null;
    }

    if(!userData.versionAtLeast('1.13.62')){
      console.log(
        `Experiment recording - User: ${userData._id} using old app version ${userData.appVersion} . Aborting save.`
      );

      return null;
    }

    //check user experiment config
    const experiments = ExperimentRecording.#getUserExperiments(userData)
    if(experiments.length === 0){
      console.log(`Experiment recording - no experiment to record for this user: ${userData._id}`);
      return null
    }

    if (userData._id) {
      const existingRecord = await ExperimentRecordingModel.findOne({userID: userData._id});
      if (existingRecord) {
        console.log(
          `Experiment recording - User ${userData._id} has already been recorded for another experiment.`
        );
        return null
      }
    }

    const platform = userData.os;
    const deviceSize = userData.deviceSize;
    const country = userData.ipData?.countryCode;
    const userID = userData._id

    const { gender, age, preferences, languages, kochava } = userData;
    const subscription = isPremium(userData) ? "premium" : "free";

    if (!userID || !platform || !deviceSize || !country) {
      console.log(
        `Experiment recording - Mandatory data not found, userID: ${userID} platform: ${platform}, deviceSize: ${deviceSize}, country: ${country}, Aborting save.`
      );
      return null;
    }

    if (
      platform.toLowerCase() !== "android" &&
      platform.toLowerCase() !== "ios"
    ) {
      console.log("Experiment recording - skip recording if device is not android / ios");
      return null;
    }

    let instance

    //loop experiments to check quota
    for (const experiment of experiments) {
      instance = new ExperimentRecording(
        platform,
        deviceSize,
        country,
        `${experiment.experimentId}=${
          userData.config[experiment.experimentId]
        }`,
        userID
      );

      instance.recordNonBinary = experiment.recordNonBinary;
      if (
        !instance.recordNonBinary &&
        userData.gender &&
        userData.gender.toLowerCase() === "non-binary"
      ) {
        console.log(`Experiment recording - User: ${userData._id}, skip recording for non-binary`);
        instance = false;
        continue;
      }

      instance.isOnBoarding = experiment.isOnBoarding;
      if (!instance.isOnBoarding) {
        instance.gender = instance.#validateGender(gender);
        instance.age = instance.#validateAndAssignAge(age);
        instance.preferences = instance.#validatePreferences(preferences);
        instance.subscription = instance.#validateSubscription(subscription);
        instance.language = languages[0] ? languages[0] : null;
        if (
          !instance.gender ||
          !instance.age ||
          !instance.preferences ||
          !instance.subscription ||
          !instance.language
        ) {
          console.log(
            `Experiment recording - user ${instance.userID}, experiment ${instance.experiment} metadata not complete for recording,gender: ${gender}, age: ${age}, preferences: ${preferences}, languages : ${languages}, subscription: ${subscription} `
          );
          instance = false;
          continue;
        }
      } else {
        if (gender && age && preferences && languages && subscription) {
          console.log(
            `Experiment recording - user ${instance.userID}, experiment ${instance.experiment} was passed onboarding, skip recording for this experiment`
          );
          instance = false;
          continue;
        }
      }

      if (kochava && kochava.network && kochava.network !== "") {
        instance.trafficSource = kochava.network;
      }

      instance.audienceKey = instance.#getAudienceKey();
      const quotaLimit = experiment.isOnBoarding ? 90 : 5;

      // Check if the quota for this audience combination is full
      const count = await ExperimentRecordingModel.countDocuments({
        audienceKey: instance.audienceKey,
      });

      if (count >= quotaLimit) {
        console.log(
          `Experiment recording - Quota for this audience ${instance.audienceKey} combination is full. skip.`
        );
        instance = false;
        continue;
      }
      //if all validation pass and instance is not false then break the loop and return instance
      if (instance) break;
    }

    return instance;
  }

  static async #initializeUXResearchRecording(userData){
    if (!userData) {
      console.log(`[UX Research] user data is required. Aborting save.`);
      return null;
    }

    if(!userData.versionAtLeast('1.13.62')){
      console.log(
        `[UX Research] User: ${userData._id} using old app version ${userData.appVersion} . Aborting save.`
      );

      return null;
    }

    if(await ExperimentRecording.#isUserEverRecorded(userData._id)){
      console.log(
        `[UX Research] User ${userData._id} has already been recorded for another experiment / research.`
      );
      return null
    }

    const platform = userData.os;
    const deviceSize = userData.deviceSize;
    const country = userData.ipData?.countryCode;
    const userID = userData._id

    const { gender, age, preferences, languages, kochava, appVersion } = userData;

    if(!languages[0]){
      console.log(
        `[UX Research] Languages not found on user ${userID}, skip recording `
      );
      return null;
    }

    const subscription = isPremium(userData) ? "premium" : "free";

    if (!userID || !platform || !deviceSize || !country) {
      console.log(
        `[UX Research] Mandatory data not found, userID: ${userID} platform: ${platform}, deviceSize: ${deviceSize}, country: ${country}, Aborting save.`
      );
      return null;
    }

    if (
      platform.toLowerCase() !== "android" &&
      platform.toLowerCase() !== "ios"
    ) {
      console.log("[UX Research] skip recording if device is not android / ios");
      return null;
    }

    let instance

    const ResearchesList = ExperimentRecording.#getResearchLists()
    //loop researches to check quota
    for(let research of ResearchesList){
      instance = new ExperimentRecording(
        platform,
        deviceSize,
        country,
        `${research.experimentId}`,
        userID
      );

      instance.appVersion = appVersion
      instance.language = languages[0];

      if(instance.#validateGender(gender)){
        instance.gender = instance.#validateGender(gender)
      }

      if(instance.#validateAndAssignAge(age)){
        instance.age = instance.#validateAndAssignAge(age);
      }

      if(instance.#validatePreferences(preferences)){
        instance.preferences = instance.#validatePreferences(preferences);
      }

      if(instance.#validateSubscription(subscription)){
        instance.subscription = instance.#validateSubscription(subscription);
      }

      if(kochava && kochava.network && kochava.network !== ''){
        instance.trafficSource = kochava.network
      }

      instance.audienceKey = instance.#getUXResearchAudienceKey();
      const quotaLimit = 10 //quota for each language on one version

      // Check if the quota for this audience combination is full
      const count = await ExperimentRecordingModel.countDocuments({
        audienceKey : instance.audienceKey
      });

      if (count >= quotaLimit) {
        console.log(
          `[UX Research] Quota for this audience ${instance.audienceKey} combination is full. skip.`
        );
        instance = false
        continue
      }
      //if all validation pass and instance is not false then break the loop and return instance
      if (instance) break;
    }
    return instance
  }

  // Static factory method to create, validate, and save the instance if the quota is not full
  // userData = object of User
  static async createAndSave(userData) {

    const experimentRecordingInstance = await ExperimentRecording.#initializeExperimentRecording(userData);
    const UXRecordingInstance = await ExperimentRecording.#initializeUXResearchRecording(userData);

    let instance
    let metadata;
    if (!experimentRecordingInstance && !UXRecordingInstance) {
      console.log(`Experiment recording - User: ${userData._id}, failed to initialize`);
      return {
        recordingTrigger: {
          record: false,
        },
      };
    }else if(experimentRecordingInstance && UXRecordingInstance){
      instance = experimentRecordingInstance
      if (
        instance.gender &&
        instance.age &&
        instance.preferences &&
        instance.subscription &&
        instance.language
      ) {
        metadata = {
          userPlatform: instance.platform,
          userDeviceSize: instance.deviceSize,
          userGender: instance.gender,
          userAge: instance.age,
          userPreferences: instance.preferences,
          userSubscription: instance.subscription,
          userID: instance.userID,
          userLanguage: instance.language,
          userConfig: `${instance.experiment}_${UXRecordingInstance.experiment}`,
          trafficSource: instance.trafficSource
        };
      } else {
        metadata = {
          userPlatform: instance.platform,
          userDeviceSize: instance.deviceSize,
          userID: instance.userID,
          userConfig: instance.experiment,
          trafficSource: instance.trafficSource
        };
      }
    }else if(experimentRecordingInstance){
      instance = experimentRecordingInstance
      if (
        instance.gender &&
        instance.age &&
        instance.preferences &&
        instance.subscription &&
        instance.language
      ) {
        metadata = {
          userPlatform: instance.platform,
          userDeviceSize: instance.deviceSize,
          userGender: instance.gender,
          userAge: instance.age,
          userPreferences: instance.preferences,
          userSubscription: instance.subscription,
          userID: instance.userID,
          userLanguage: instance.language,
          userConfig: instance.experiment,
          trafficSource: instance.trafficSource
        };
      } else {
        metadata = {
          userPlatform: instance.platform,
          userDeviceSize: instance.deviceSize,
          userID: instance.userID,
          userConfig: instance.experiment,
          trafficSource: instance.trafficSource
        };
      }

    }else if(UXRecordingInstance){
      instance = UXRecordingInstance
      metadata = {
        userPlatform: instance.platform,
        userDeviceSize: instance.deviceSize,
        userGender: instance.gender,
        userAge: instance.age,
        userPreferences: instance.preferences,
        userSubscription: instance.subscription,
        userID: instance.userID,
        userLanguage: instance.language,
        userConfig: instance.experiment,
        trafficSource: instance.trafficSource
      }
    }



    try {
      //logging to debug missing data
      const { gender, age, preferences, languages } = userData;
      console.log(`Experiment recording - record for user: ${userData._id}. recordingAllocationEvaluated : ${userData._id.recordingAllocationEvaluated} `)
      console.log(`Experiment recording - userData: gender: ${gender}, age: ${age}, preferences: ${preferences}, languages : ${languages}`);
      console.log('Experiment recording - experimentData :', instance)
      const record = await new ExperimentRecordingModel(instance).save();
      return {
        recordingTrigger: {
          record: true,
          metadata,
        },
      };
    } catch (error) {
      console.error("Experiment recording - Error saving experiment recording:", error);
      return {
        recordingTrigger: {
          record: false,
        },
      };
    }
  }
}

module.exports = ExperimentRecording;
