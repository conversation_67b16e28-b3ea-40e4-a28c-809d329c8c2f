const fs = require('fs');
const { parse } = require('csv-parse/sync');

const pathToCsv = `${__dirname}/ethnicities.csv`;
const input = fs.readFileSync(pathToCsv);
const records = parse(input, {
  columns: true,
  skip_empty_lines: true,
  trim: true,
});

const ethnicities = [];
for (const record of records) {
  if (record['Level 1']) {
    ethnicities.push(record['Level 1']);
  }
  if (record['Level 2']) {
    ethnicities.push(record['Level 2']);
  }
  if (record['Level 3']) {
    ethnicities.push(record['Level 3']);
  }
}

ethnicities.push('Berber');

module.exports = {
  ethnicities,
}
