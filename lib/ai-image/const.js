


let WEBHOOK_ENDPOINT = 'FAKE WEBHOOK ENPOINT'
if(process.env.NODE_ENV === 'beta'){
    WEBHOOK_ENDPOINT = 'https://api.beta.boo.dating/aiimagewebhook'
}else if (process.env.NODE_ENV === 'prod'){
    WEBHOOK_ENDPOINT = 'https://api.prod.boo.dating/aiimagewebhook'
}

const AI_IMAGE_NUMBER_OF_RESULTS = process.env.AI_IMAGE_NUMBER_OF_RESULTS || 5

const taskStatus = Object.freeze({
    QUEUE: "QUEUE",
    PENDING: "PENDING",
    FAILURE: "FAILURE",
    SUCCESS: "SUCCESS"
});

const batchStatus = Object.freeze({
    QUEUE: "QUEUE",
    PENDING: "PENDING",
    FAILURE: "FAILURE",
    DONE: "DONE",
    EXPIRED: "EXPIRED",
    SELECTED: "SELECTED",
    REGENERATED: "REGENERATED",
    REMOVED: "REMOVED",
});

const suggestionType = Object.freeze({
    VISUAL: 'visual style',
    TEXT: 'text suggestion',
  });

const IMGPLN_SERVER_ID = process.env.IMGPLN_SERVER_ID || "any"

module.exports = ({
    taskStatus, suggestionType, WEBHOOK_ENDPOINT, batchStatus, AI_IMAGE_NUMBER_OF_RESULTS, IMGPLN_SERVER_ID
})
