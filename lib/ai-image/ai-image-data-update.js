const httpErrors = require("../http-errors");
const AIImage = require('../../models/ai-image');
const admin = require('../../config/firebase-admin');
const { translate } = require('../translate');
const { batchStatus, taskStatus } = require("../ai-image/const")
const { sendSocketEvent } = require('../socket');
const HiveService = require('../image-moderation/hive');
const ImageModeration = require('../../models/image-moderation');


async function handle(data) {
  const hive =  new HiveService()

  try {
    if (data.status && (data.status === "PENDING" || data.status === "FAILURE") ) {
      const result = await updateImageOutputsByTaskId(data.batch_id, data.task_id, data.status)
      if(data.status === "FAILURE"){
        await isResultReady(data.batch_id)
      }
      return result
    } else if (data.download_urls.length > 0) {
      const imageUrl = data.download_urls[0].webp
      const imageModeration = await hive.moderatePictureByUrl(imageUrl)
      await ImageModeration.create({
        url: imageUrl,
        moderationLabels: imageModeration.detectionLabels,
        isFlagged: imageModeration.isFlagged,
        flaggedModerationLabel: imageModeration.flaggedModerationLabel || undefined,
        serviceName: 'hive',
      });
      let result
      if(!imageModeration.isFlagged){
        result = await updateImageOutputsByTaskId(data.batch_id, data.task_id, taskStatus.SUCCESS, imageUrl)
        await isResultReady(data.batch_id)
      }else{
        result = await updateImageOutputsByTaskId(data.batch_id, data.task_id, taskStatus.FAILURE, imageUrl, `Failed to pass moderation with class: ${imageModeration.detectionLabels[0].Name} - ${imageModeration.detectionLabels[0].Confidence.toFixed(1)}`)
        await isResultReady(data.batch_id)
      }
      return result
      
    } else {
      throw `receive unrecognize data structuture: ${JSON.stringify(data, null, 2)}`;
    }
  } catch (error) {
    throw error;
  }
}

async function isResultReady(batchId){
  // check results status
  // if there is no pending status left on result object then send notification to user
  const aiImageData = await AIImage.findOne({ _id: batchId, batchStatus: { $in: [batchStatus.QUEUE, batchStatus.PENDING]} }).populate('user');
  
  const data = { openPage: "aiImagesResults"}

  if(aiImageData.results.every((result) => result.status === 'FAILURE')){
    aiImageData.batchStatus = batchStatus.FAILURE
    await aiImageData.save()

    sendSocketEvent(aiImageData.user._id, 'ai image ready', {batchId : batchId, imageKey: aiImageData.originalImage});

    //send notification
    admin.sendNotification(
      aiImageData.user,
      null,
      translate('Try Again', aiImageData.user.locale),
      translate('There was an issue generating your AI photos, please try again.', aiImageData.user.locale),
      data,
      null,
      'general',
      'ai-image-ready',
    );
  }else if(!aiImageData.results.some(result => result.status === 'PENDING')){
    aiImageData.batchStatus = batchStatus.DONE
    await aiImageData.save()

    sendSocketEvent(aiImageData.user._id, 'ai image ready', {batchId : batchId, imageKey: aiImageData.originalImage});

    //send notification
    admin.sendNotification(
      aiImageData.user,
      null,
      translate('Your AI Photos Are Ready ✨', aiImageData.user.locale),
      translate('You look amazing. Check out your new profile pics.', aiImageData.user.locale),
      data,
      null,
      'general',
      'ai-image-ready',
    );
  }
  
}

async function updateImageOutputsByTaskId(batch_id, task_id, status, imageOutputs = undefined, error = null ) {
  try {
    const result = await AIImage.findOneAndUpdate(
      { _id: batch_id }, // Match the document by batch_id
      {
        $set: {
          "results.$[elem].status": status,
          "results.$[elem].imageUrl": imageOutputs,
          "results.$[elem].error": error,
          "results.$[elem].updatedAt": new Date()
        }
      },
      {
        arrayFilters: [
          { "elem.id": task_id } // Filter to match the specific element
        ],
        new: true // Return the updated document
      }
    );
  
    if (!result) {
      console.log(`AI Image data not found batch_id : ${batch_id}, task_id: ${task_id}`);
      throw httpErrors.notFoundError();
    }

    // Check if the specific task_id was updated
    const taskUpdated = result.results.some((result) => result.id === task_id);
    if (!taskUpdated) {
      console.log(`Task ID not found or no changes made, task_id: ${task_id}`);
      throw httpErrors.notFoundError();
    }
  
    return result;
  } catch (error) {
    throw error;
  }
}

module.exports = {handle, updateImageOutputsByTaskId};