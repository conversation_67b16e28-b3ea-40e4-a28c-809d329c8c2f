const _ = require('underscore');
const deepFreeze = require('deep-freeze');

const promptsArray = [
  {
    id: '12',
    prompt: 'I want someone who',
    exampleAnswer: 'Brings me food.',
  },
  {
    id: '11',
    prompt: 'All I ask is that you',
    exampleAnswer: 'Be authentic.',
  },
  {
    id: '23',
    prompt: 'We\'ll get along if',
    exampleAnswer: 'You take the lead.',
  },
  {
    id: '30',
    prompt: 'I get way too excited about',
    exampleAnswer: 'Puppies!',
  },
  {
    id: '18',
    prompt: 'My idea of fun is',
    exampleAnswer: 'An afternoon doing math and solving puzzles 🤓',
  },
  {
    id: '20',
    prompt: 'The quickest way to my heart is',
    exampleAnswer: 'You like <PERSON> & Mo<PERSON>y and <PERSON>tar the Last Airbender.',
  },
  {
    id: '14',
    prompt: 'My Love Language is',
    exampleAnswer: 'Options: Quality time, Words of affirmation, Acts of service, Gifts, and Physical touch.',
  },
  {
    id: '8',
    prompt: 'I\'m crazy for',
    exampleAnswer: 'A great sense of humor.',
  },
  {
    id: '34',
    prompt: 'My greatest dream is',
    exampleAnswer: 'Building a house one day by a lake with my art earnings.',
  },
  {
    id: '19',
    prompt: 'Together, we could',
    exampleAnswer: 'Rule the world.',
  },
  {
    id: '33',
    prompt: 'I\'m a sucker for',
    exampleAnswer: 'Movies with beautiful storytelling.',
  },
  {
    id: '27',
    prompt: 'I\'m weirdly attracted to',
    exampleAnswer: 'Intelligence',
  },
  {
    id: '29',
    prompt: 'Tell me about',
    exampleAnswer: 'Your favorite poet. Go!',
  },
  {
    id: '25',
    prompt: 'I\'m not right for you if',
    exampleAnswer: 'You like celebrity gossip.',
  },
  {
    id: '4',
    prompt: 'Dating me is like',
    exampleAnswer: 'Exploring the universe together. It\'ll be an adventure!',
  },
  {
    id: '17',
    prompt: 'My life philosophy is',
    exampleAnswer: 'YOLO.',
  },
  {
    id: '2',
    prompt: 'The best way to ask me out is by',
    exampleAnswer: 'Slam poetry.',
  },
  {
    id: '24',
    prompt: 'What most people don\'t know about me',
    exampleAnswer: 'I\'m quiet in the beginning but super extroverted once we\'re close.',
  },
  {
    id: '26',
    prompt: 'Tell me',
    exampleAnswer: 'What is your life philosophy?',
  },
  {
    id: '6',
    prompt: 'I\'m super proud of',
    exampleAnswer: 'My action figure collection.',
  },
  {
    id: '21',
    prompt: 'I\'ll know I\'ve found the one when',
    exampleAnswer: 'I think you just might be weirder than me.',
  },
  {
    id: '1',
    prompt: 'What\'s non-negotiable for me is',
    exampleAnswer: 'Values',
  },
  {
    id: '35',
    prompt: 'I think the world would be better off if',
    exampleAnswer: 'Recycling was mandated by law.',
  },
  {
    id: '31',
    prompt: 'I secretly think',
    exampleAnswer: 'Aliens exist.',
  },
  {
    id: '22',
    prompt: 'The dorkiest thing about me is',
    exampleAnswer: 'I read the Reddit conspiracy theories section religiously. BTW, Big Foot is real.',
  },
  {
    id: '3',
    prompt: 'Show me you also',
    exampleAnswer: 'Have an open mind for trying new things.',
  },
  {
    id: '5',
    prompt: 'We\'re the same type of weird if',
    exampleAnswer: 'You avoid stepping on cracks.',
  },
  {
    id: '10',
    prompt: 'A secret of mine',
    exampleAnswer: 'I spend too much time on the Internet learning about random facts.',
  },
  {
    id: '7',
    prompt: 'I\'ll introduce you to my family if',
    exampleAnswer: 'You can make my grandma laugh.',
  },
  {
    id: '9',
    prompt: 'A social issue I care about',
    exampleAnswer: 'World peace.',
  },
  {
    id: '13',
    prompt: 'My most controversial opinion is',
    exampleAnswer: 'The earth is flat.',
  },
  {
    id: '16',
    prompt: 'Change my mind about',
    exampleAnswer: 'Pineapple on pizza',
  },
  {
    id: '28',
    prompt: 'Debate me',
    exampleAnswer: 'Time travel should be a higher scientific priority.',
  },
  {
    id: '15',
    prompt: 'I like to humble brag that',
    exampleAnswer: 'I\'m well balanced on the Big 5 scale.',
  },
  {
    id: '32',
    prompt: 'My recent intellectual muse is',
    exampleAnswer: 'The psychology of decision-making.',
  },
];
deepFreeze(promptsArray);

const promptsMap = promptsArray.reduce((map, prompt) => {
  map[prompt.id] = prompt;
  return map;
}, {});
deepFreeze(promptsMap);

function getPromptFromId(id) {
  const prompt = promptsMap[id];
  if (!prompt) {
    return null;
  }
  return prompt.prompt;
}

function getPromptText(prompt) {
  if (prompt.prompt) {
    return prompt.prompt;
  } else if (prompt.id) {
    const promptFromId = promptsMap[prompt.id];
    if (!promptFromId) {
      return null;
    }
    return promptFromId.prompt;
  }
  return null;
}

function filterPrompts(prompts) {
  if (!Array.isArray(prompts)) {
    return [];
  }
  return prompts.filter((p) => p?.id !== undefined);
}

function getFormattedPrompts(prompts, locale) {
  if (!Array.isArray(prompts)) {
    return [];
  }
  const formatted = prompts.filter((p) => promptsMap[p.id] !== undefined).map((p) => ({
    id: p.id,
    prompt: promptsMap[p.id].prompt,
    answer: p.answer,
  }));
  if (formatted.length > 3) {
    return formatted.slice(0, 3);
  }
  return formatted;
}

module.exports = {
  promptsArray,
  promptsMap,
  getFormattedPrompts,
  getPromptFromId,
  getPromptText,
  filterPrompts,
};
